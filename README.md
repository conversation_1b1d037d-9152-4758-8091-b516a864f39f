# Introduction
This repository is a clone of [tv_flutter_sdk_ejfwkqvmfdydhurlxkac](https://github.com/tsocial-partner/tv_flutter_sdk_ejfwkqvmfdydhurlxkac).
Updates to this repository should be made following any releases in the original repository.

# Purpose of This Repository
Using the repository belonging to https://github.com/tsocial-partner requires the EVO Mobile team to manually rotate the access token, which expires after a short period of time (currently every 3 months). This repository serves as a mirror for the Trust Vision Flutter SDK, allowing the EVO App to use it without the hassle of frequent token rotation.
