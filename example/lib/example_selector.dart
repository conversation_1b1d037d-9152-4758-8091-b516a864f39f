// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

/// Main entry point for the example app.
///
/// This file provides a selector to navigate between different examples
/// demonstrating various features of the Flutter Common Package.
void main() {
  runApp(const ExampleSelectorApp());
}

/// Example selector app.
class ExampleSelectorApp extends StatelessWidget {
  const ExampleSelectorApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Common Package Examples',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const ExampleSelectorHome(),
    );
  }
}

/// Home screen for the example selector.
class ExampleSelectorHome extends StatelessWidget {
  const ExampleSelectorHome({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flutter Common Package Examples'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            _buildExampleCard(
              context,
              title: 'Comprehensive Modular System Example',
              description: 'Demonstrates all aspects of the modular system: initialization, custom modules, and host app integration.',
              route: '/host',
              command: 'flutter run -t lib/host_app_example.dart',
              isHighlighted: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExampleCard(
    BuildContext context, {
    required String title,
    required String description,
    required String route,
    required String command,
    bool isHighlighted = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isHighlighted ? 4 : 2,
      color: isHighlighted ? Colors.blue.shade50 : null,
      child: InkWell(
        onTap: () {
          _showRunInstructions(context, title, command);
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (isHighlighted)
                    const Chip(
                      label: Text('Recommended'),
                      backgroundColor: Colors.blue,
                      labelStyle: TextStyle(color: Colors.white),
                    ),
                  const Spacer(),
                  ElevatedButton(
                    onPressed: () {
                      _showRunInstructions(context, title, command);
                    },
                    child: const Text('Run Example'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showRunInstructions(BuildContext context, String title, String command) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Run $title'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'To run this example, execute the following command in your terminal:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(4),
              ),
              child: SelectableText(
                command,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
