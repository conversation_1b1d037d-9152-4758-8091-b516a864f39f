// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

/// Constants for common package module names.
///
/// These constants should be used when referring to modules by name,
/// for example when initializing specific modules.
///
/// Example:
/// ```dart
/// await initCommonPackage(
///   features: [
///     CommonPackageModuleNames.core,
///     CommonPackageModuleNames.network,
///     CommonPackageModuleNames.ui,
///   ],
/// );
/// ```
class CommonPackageModuleNames {

  /// Core module - provides basic functionality
  static const String core = 'core';

  /// Network module - provides HTTP client and network utilities
  static const String network = 'network';

  /// Analytics module - provides analytics tracking
  static const String analytics = 'analytics';

  /// Device info module - provides device information
  static const String deviceInfo = 'device_info';

  /// Data collection module - provides data collection utilities
  static const String dataCollection = 'data_collection';

  /// Notification module - provides notification handling
  static const String notification = 'notification';

  /// UI module - provides UI components
  static const String ui = 'ui';

  /// Utility module - provides utility features
  static const String utility = 'utility';

  /// eKYC module - provides eKYC functionality
  static const String ekyc = 'ekyc';
}
