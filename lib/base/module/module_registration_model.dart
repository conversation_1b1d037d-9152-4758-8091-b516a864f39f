// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'feature_module.dart';

/// Details about a module registration.
///
/// This class tracks metadata about a module registration, including
/// the source of the module and when it was registered.
class ModuleRegistrationModel {
  /// The module instance.
  final FeatureModule module;

  /// The source of the module (e.g., 'common_package', 'host_app').
  final String source;

  /// When the module was registered.
  final DateTime registeredAt;

  /// Creates a new [ModuleRegistrationModel].
  ModuleRegistrationModel({
    required this.module,
    required this.source,
    required this.registeredAt,
  });

  /// Returns a string representation of this registration.
  ///
  /// Includes the module name, source, and registration time for easier debugging.
  @override
  String toString() {
    return 'ModuleRegistration{module: ${module.name}, source: $source, registeredAt: $registeredAt}';
  }
}
