// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:meta/meta.dart';

import '../../util/extension.dart';
import 'feature_module.dart';
import 'module_registration_model.dart';



/// Result of a dependency validation operation.
///
/// Contains information about whether validation passed and any cycles detected.
class ModuleDependencyValidationResult {
  /// Whether the validation passed (no cycles detected).
  final bool isValid;

  /// List of detected dependency cycles, if any.
  ///
  /// Each cycle is represented as a list of module names forming the cycle.
  final List<List<String>> detectedCycles;

  /// Error message describing the validation failure, if any.
  final String? errorMessage;

  /// Creates a validation result.
  const ModuleDependencyValidationResult({
    required this.isValid,
    this.detectedCycles = const <List<String>>[],
    this.errorMessage,
  });

  /// Creates a successful validation result.
  const ModuleDependencyValidationResult.success()
      : isValid = true,
        detectedCycles = const <List<String>>[],
        errorMessage = null;

  /// Creates a failed validation result with the specified cycles and error message.
  const ModuleDependencyValidationResult.failure({
    required this.detectedCycles,
    required this.errorMessage,
  }) : isValid = false;
}

/// Comprehensive validation utilities for the module system.
///
/// This class provides all validation logic used across the module system,
/// including dependency cycle detection, module name validation, and
/// single module registration validation.
///
/// Used by both [ModuleRegistryBuilder] and [ModuleRegistry] to ensure
/// consistent validation behavior across the entire module system.
class ModuleValidators {
  /// Validates module dependencies for circular dependencies.
  ///
  /// [moduleRegistrations] - The current set of module registrations
  /// [newModule] - Optional new module being added (included in validation)
  ///
  /// Returns a [ModuleDependencyValidationResult] indicating whether validation passed.
  static ModuleDependencyValidationResult validateDependencies(
    Map<String, ModuleRegistrationModel> moduleRegistrations, {
    FeatureModule? newModule,
  }) {
    try {
      // Include new module in validation if provided
      final registrationsToValidate = <String, ModuleRegistrationModel>{
        ...moduleRegistrations,
      };

      if (newModule != null) {
        registrationsToValidate[newModule.name] = ModuleRegistrationModel(
          module: newModule,
          source: 'temp_validation',
          registeredAt: DateTime.now(),
        );
      }

      return _validateComplete(registrationsToValidate);
    } catch (e) {
      'ModuleDependencyValidator'.commonLog('Validation error: $e');
      return ModuleDependencyValidationResult.failure(
        detectedCycles: const <List<String>>[],
        errorMessage: e.toString(),
      );
    }
  }

  /// Performs complete validation of all module dependencies.
  static ModuleDependencyValidationResult _validateComplete(
    Map<String, ModuleRegistrationModel> moduleRegistrations,
  ) {
    final dependencyGraph = _buildDependencyGraph(moduleRegistrations);
    return _detectCycles(dependencyGraph);
  }

  /// Builds a dependency graph from module registrations.
  ///
  /// Returns a map where keys are module names and values are sets of
  /// module names that the key module depends on.
  static Map<String, Set<String>> _buildDependencyGraph(
    Map<String, ModuleRegistrationModel> moduleRegistrations,
  ) {
    final Map<String, Set<String>> dependencyGraph = <String, Set<String>>{};
    final Map<Type, String> modulesByType = <Type, String>{};

    // Map types to providing modules
    for (final ModuleRegistrationModel registration in moduleRegistrations.values) {
      final List<Type> dependencies = registration.module.dependencies;
      for (final Type type in dependencies) {
        modulesByType[type] = registration.module.name;
      }
    }

    // Build the dependency graph
    for (final ModuleRegistrationModel registration in moduleRegistrations.values) {
      final String moduleName = registration.module.name;
      final Set<String> moduleDependencies = <String>{};
      final List<Type> dependencies = registration.module.dependencies;

      for (final Type typeDependency in dependencies) {
        final String? provider = modulesByType[typeDependency];
        if (provider != null && provider != moduleName) {
          moduleDependencies.add(provider);
        }
      }

      dependencyGraph[moduleName] = moduleDependencies;
    }

    return dependencyGraph;
  }

  /// Detects cycles in the complete dependency graph.
  static ModuleDependencyValidationResult _detectCycles(
    Map<String, Set<String>> dependencyGraph,
  ) {
    final Set<String> visited = <String>{};
    final Set<String> recursionStack = <String>{};
    final List<List<String>> detectedCycles = <List<String>>[];

    for (final String moduleName in dependencyGraph.keys) {
      final List<String> currentPath = <String>[];
      if (_detectCycleRecursive(
        moduleName,
        dependencyGraph,
        visited,
        recursionStack,
        currentPath,
        detectedCycles,
      )) {
        // Cycle detected - continue checking other modules for additional cycles
      }
    }

    if (detectedCycles.isNotEmpty) {
      final String errorMessage = _buildCycleErrorMessage(detectedCycles);
      return ModuleDependencyValidationResult.failure(
        detectedCycles: detectedCycles,
        errorMessage: errorMessage,
      );
    }

    return const ModuleDependencyValidationResult.success();
  }



  /// Recursive helper method for cycle detection using depth-first search.
  ///
  /// Returns true if a cycle is detected, false otherwise.
  /// Populates [detectedCycles] with any cycles found.
  static bool _detectCycleRecursive(
    String moduleName,
    Map<String, Set<String>> graph,
    Set<String> visited,
    Set<String> recursionStack,
    List<String> currentPath,
    List<List<String>> detectedCycles,
  ) {
    // If not visited, mark as visited and add to recursion stack
    if (!visited.contains(moduleName)) {
      visited.add(moduleName);
      recursionStack.add(moduleName);
      currentPath.add(moduleName);

      // Visit all dependencies
      for (final String dependency in graph[moduleName] ?? <String>{}) {
        // If dependency is in recursion stack, we found a cycle
        if (recursionStack.contains(dependency)) {
          // Build the cycle path
          final int cycleStartIndex = currentPath.indexOf(dependency);
          final List<String> cycle = currentPath.sublist(cycleStartIndex);
          cycle.add(dependency); // Complete the cycle
          detectedCycles.add(cycle);
          return true;
        }

        // If dependency not visited and leads to a cycle
        if (!visited.contains(dependency) &&
            _detectCycleRecursive(
              dependency,
              graph,
              visited,
              recursionStack,
              currentPath,
              detectedCycles,
            )) {
          return true;
        }
      }

      // Remove from current path when backtracking
      currentPath.removeLast();
    }

    // Remove from recursion stack when backtracking
    recursionStack.remove(moduleName);
    return false;
  }

  /// Builds a detailed error message for detected cycles.
  static String _buildCycleErrorMessage(List<List<String>> detectedCycles) {
    if (detectedCycles.isEmpty) {
      return 'No cycles detected';
    }

    final StringBuffer buffer = StringBuffer();
    buffer.writeln('Circular dependency detected in module graph.');

    if (detectedCycles.length == 1) {
      final List<String> cycle = detectedCycles.first;
      final String cycleStr = cycle.join(' → ');
      buffer.write('Dependency cycle: $cycleStr');
    } else {
      buffer.writeln('Multiple dependency cycles detected:');
      for (int i = 0; i < detectedCycles.length; i++) {
        final List<String> cycle = detectedCycles[i];
        final String cycleStr = cycle.join(' → ');
        buffer.writeln('  ${i + 1}. $cycleStr');
      }
    }

    return buffer.toString().trim();
  }

  /// Validates module names for uniqueness across different sources.
  ///
  /// This is a utility method that can be used alongside dependency validation
  /// to ensure module name uniqueness.
  @visibleForTesting
  static void validateModuleNames(Map<String, ModuleRegistrationModel> moduleRegistrations) {
    // Group modules by name
    final Map<String, List<ModuleRegistrationModel>> modulesByName = <String, List<ModuleRegistrationModel>>{};

    for (final MapEntry<String, ModuleRegistrationModel> entry in moduleRegistrations.entries) {
      modulesByName.putIfAbsent(entry.key, () => <ModuleRegistrationModel>[]).add(entry.value);
    }

    // Find duplicates
    final List<MapEntry<String, List<ModuleRegistrationModel>>> duplicates = modulesByName.entries
        .where((MapEntry<String, List<ModuleRegistrationModel>> entry) => entry.value.length > 1)
        .toList();

    if (duplicates.isNotEmpty) {
      final String errorMessage = 'Duplicate module names detected:\n${duplicates.map((MapEntry<String, List<ModuleRegistrationModel>> entry) {
        final String modulesInfo = entry.value.map((ModuleRegistrationModel reg) =>
            '${reg.module.runtimeType} from "${reg.source}"').join(', ');
        return '- "${entry.key}" is used by: $modulesInfo';
      }).join('\n')}';

      throw Exception(errorMessage);
    }
  }

  /// Validates a single module registration for basic constraints.
  ///
  /// Checks for module name conflicts and validates basic module properties.
  /// This method validates that:
  /// - Module has a non-empty name
  /// - Module name doesn't conflict with existing modules (unless same type)
  ///
  /// [module] - The module to validate
  /// [source] - The source identifier for the module
  /// [existingRegistrations] - Current module registrations to check against
  ///
  /// Throws an exception if validation fails.
  @visibleForTesting
  static void validateSingleModuleRegistration(
    FeatureModule module,
    String source,
    Map<String, ModuleRegistrationModel> existingRegistrations,
  ) {
    // Validate module name
    if (module.name.isEmpty) {
      throw Exception(
        'Invalid module: Module name cannot be empty. '
        'Module of type ${module.runtimeType} from "$source" must have a non-empty name.'
      );
    }

    // Check if a module with the same name is already registered
    if (existingRegistrations.containsKey(module.name)) {
      final ModuleRegistrationModel existing = existingRegistrations[module.name]!;

      // If the same module type is registered again, it's fine (will be replaced)
      if (existing.module.runtimeType == module.runtimeType) {
        'ModuleValidators'.commonLog(
          'Module "${module.name}" of type ${module.runtimeType} will replace '
          'existing registration from "${existing.source}" with new registration from "$source"',
        );
        return;
      }

      // Otherwise, we have a conflict
      throw Exception(
        'Module name conflict detected: "${module.name}" is already '
        'registered by ${existing.module.runtimeType} from "${existing.source}". '
        'New module ${module.runtimeType} from "$source" cannot use the same name. '
        'Each module must have a unique name across all sources.'
      );
    }
  }

  /// Validates all modules in a comprehensive manner.
  ///
  /// This method performs all validation checks including:
  /// - Module name uniqueness validation
  /// - Dependency cycle detection
  ///
  /// [moduleRegistrations] - The complete set of module registrations to validate
  ///
  /// Throws an exception if any validation fails.
  static void validateAllModules(Map<String, ModuleRegistrationModel> moduleRegistrations) {
    // Check for duplicate module names across different sources
    validateModuleNames(moduleRegistrations);

    // Validate dependency graph for circular dependencies
    final result = validateDependencies(moduleRegistrations);
    if (!result.isValid) {
      throw Exception(result.errorMessage);
    }
  }
}
