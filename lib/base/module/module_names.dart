// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

/// Constants for module names used throughout the application.
///
/// This class centralizes all module names to ensure consistency
/// and avoid typos when referencing modules by name.
class CommonPackageModuleNames {
  /// Core module that provides essential dependencies
  static const String core = 'common_core';

  /// Network module that provides networking dependencies
  static const String network = 'common_network';

  /// Analytics module that provides analytics and logging dependencies
  static const String analytics = 'common_analytics';

  /// Data collection module that provides data collection dependencies
  static const String dataCollection = 'common_data_collection';

  /// Notification module that provides push notification dependencies
  static const String notification = 'common_notification';

  /// UI module that provides UI-related dependencies
  static const String ui = 'common_ui';

  /// Utility module that provides utility dependencies
  static const String utility = 'common_utility';

  /// eKYC module that provides electronic Know Your Customer dependencies
  static const String ekyc = 'common_ekyc';

  /// Device info module that provides device-related information and identification
  static const String deviceInfo = 'common_device_info';

// No test modules - using <PERSON>cktail for testing instead
}
