// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:convert';

import '../../common_package/common_package.dart';
import '../../feature/ekyc/bridges/models/ekyc_bridge_image.dart';
import 'ekyc_request.dart';

class EkycUploadImagesRequest extends EkycRequest {
  List<EkycBridgeImage> images;
  String label;

  EkycUploadImagesRequest({
    required this.images,
    required super.sessionToken,
    this.label = 'portrait',
  });

  Map<String, dynamic> toJson() {
    final List<MultipartFile> files = <MultipartFile>[];
    for (final EkycBridgeImage image in images) {
      final String? rawImageBase64 = image.rawImageBase64;
      if (rawImageBase64 == null) continue;

      final MultipartFile file = MultipartFile.fromBytes(
        const Base64Decoder().convert(rawImageBase64),
        filename: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      files.add(file);
    }
    return <String, dynamic>{
      'label': label,
      'file': files,
    };
  }
}
