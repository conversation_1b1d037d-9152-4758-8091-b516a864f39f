// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../../http_client/mock_config.dart';
import '../../request/ekyc_upload_frames_request.dart';
import '../../request/ekyc_upload_images_request.dart';
import '../../response/ekyc_client_settings_entity.dart';
import '../../response/ekyc_upload_frames_entity.dart';
import '../../response/ekyc_upload_images_entity.dart';

abstract class EkycRepo {
  // refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3840050035/ES+UM+Replace+FaceOTP+to+FaceAuthen#5.2.3.-Get-client-settings
  // swagger: https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/EKYC/handleEKYCClientSettings
  Future<EkycClientSettingsEntity> getClientSettings({
    String? sessionToken,
    MockConfig? mockConfig,
  });

  // refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3840050035/ES+UM+Replace+FaceOTP+to+FaceAuthen#5.2.5.-Upload-images
  // swagger: https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/EKYC/handleEKYCUploadImages
  Future<EkycUploadImagesEntity> uploadImages({
    required EkycUploadImagesRequest request,
    MockConfig? mockConfig,
  });

  // refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3840050035/ES+UM+Replace+FaceOTP+to+FaceAuthen#5.2.4.-Upload-frames
  // swagger: https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/EKYC/handleEKYCUploadFrames
  Future<EkycUploadFramesEntity> uploadFrames({
    required EkycUploadFramesRequest request,
    MockConfig? mockConfig,
  });
}
