// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:dio/dio.dart';

import 'common_request_option.dart';

class DioRequestOptionMapper {
  final Duration? receiveTimeout;
  final Duration? sendTimeout;

  DioRequestOptionMapper({
    this.receiveTimeout,
    this.sendTimeout,
  });

  Options map(CommonRequestOption? requestOption) {
    final Options options = Options(
      receiveTimeout: receiveTimeout,
      sendTimeout: sendTimeout,
    );

    if (requestOption?.headers?.isNotEmpty == true) {
      options.headers = requestOption?.headers;
    }

    if (requestOption?.receiveTimeout != null) {
      options.receiveTimeout = requestOption?.receiveTimeout;
    }

    if (requestOption?.sendTimeout != null) {
      options.sendTimeout = requestOption?.sendTimeout;
    }

    return options;
  }
}
