class TVDetectIDCardsResult {
  List<CardInfo>? cards;
  String? requestId;
  String? timestamp;
  String? verdict;

  TVDetectIDCardsResult.fromMap(Map<Object?, dynamic> map) {
    requestId = map['requestId'];
    timestamp = map['timestamp'];
    cards = (map['cards'] as List?)?.map((dynamic e) => CardInfo.fromMap(e as Map<Object?, dynamic>)).toList();
    verdict = map['verdict'];
  }
}

class CardInfo {
  String? cardLabel;

  CardInfo({this.cardLabel});

  CardInfo.fromMap(Map<Object?, dynamic> map) {
    cardLabel = map['cardLabel'];
  }
}
