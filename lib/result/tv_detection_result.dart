import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:trust_vision_plugin/result/action_mode.dart';
import 'package:trust_vision_plugin/result/selfie_image.dart';
import 'package:trust_vision_plugin/result/tv_card_id.dart';
import 'package:trust_vision_plugin/result/tv_card_info_result.dart';
import 'package:trust_vision_plugin/result/tv_card_type.dart';
import 'package:trust_vision_plugin/result/tv_image_class.dart';
import 'package:trust_vision_plugin/result/tv_liveness_result.dart';
import 'package:trust_vision_plugin/result/tv_nfc_info_result.dart';
import 'package:trust_vision_plugin/result/tv_sanity_result.dart';

class TVDetectionResult {
  TVLivenessResult? livenessResult;
  TVCardInfoResult? cardInfoResult;
  TVCardType? cardType;
  TVActionMode? actionMode;
  TVSanityResult? selfieSanityResult;
  TVSanityResult? idSanityResult;
  TVSanityResult? idTamperingResult;
  TVImageClass? idFrontImage;
  TVImageClass? idBackImage;
  TVImageClass? nfcImage;
  TVCardQr? frontIdQr;
  List<SelfieImage>? selfieImages;
  TVNfcInfoResult? nfcInfoResult;
  List<Uint8List>? selfieVideos;
  List<String>? livenessFrameBatchIds;

  TVDetectionResult.fromMap(Map<String, dynamic> map) {
    cardType = (map['cardType'] as Map<Object?, dynamic>?) != null
        ? TVCardType.fromMap(map['cardType'] as Map<Object?, dynamic>)
        : null;
    actionMode = TVActionMode.fromValue(map['actionMode'] as String?);
    selfieSanityResult = (map['selfieSanityResult'] as Map<Object?, dynamic>?) != null
        ? TVSanityResult.fromMap(map['selfieSanityResult'] as Map<Object?, dynamic>)
        : null;
    livenessResult = (map['livenessResult'] as Map<Object?, dynamic>?) != null
        ? TVLivenessResult.fromMap(map['livenessResult'] as Map<Object?, dynamic>)
        : null;
    cardInfoResult = (map['cardInfoResult'] as Map<Object?, dynamic>?) != null
        ? TVCardInfoResult.fromMap(map['cardInfoResult'] as Map<Object?, dynamic>)
        : null;
    idSanityResult = (map['idSanityResult'] as Map<Object?, dynamic>?) != null
        ? TVSanityResult.fromMap(map['idSanityResult'])
        : null;
    idTamperingResult = (map['idTamperingResult'] as Map<Object?, dynamic>?) != null
        ? TVSanityResult.fromMap(map['idTamperingResult'] as Map<Object?, dynamic>)
        : null;
    idFrontImage = (map['idFrontImage'] as Map<Object?, dynamic>?) != null
        ? TVImageClass.fromMap(map['idFrontImage'] as Map<Object?, dynamic>)
        : null;
    idBackImage = (map['idBackImage'] as Map<Object?, dynamic>?) != null
        ? TVImageClass.fromMap(map['idBackImage'] as Map<Object?, dynamic>)
        : null;
    nfcImage = (map['nfcImage'] as Map<Object?, dynamic>?) != null
        ? TVImageClass.fromMap(map['nfcImage'] as Map<Object?, dynamic>)
        : null;
    frontIdQr = (map['frontIdQr'] as Map<Object?, dynamic>?) != null
        ? TVCardQr.fromMap(map['frontIdQr'] as Map<Object?, dynamic>)
        : null;
    selfieImages = (map['selfieImages'] as List?)
        ?.map((dynamic e) => SelfieImage.fromMap(e as Map<Object?, dynamic>))
        .toList();
    nfcInfoResult = (map['nfcInfoResult'] as Map<Object?, dynamic>?) != null
        ? TVNfcInfoResult.fromMap(map['nfcInfoResult'] as Map<Object?, dynamic>)
        : null;

    livenessFrameBatchIds = (map['livenessFrameBatchIds'] as List?)?.map((dynamic e) => e as String).toList();

    if (Platform.isAndroid) {
      selfieVideos = (map['selfieVideos'] as List?)?.map((dynamic e) => e as Uint8List).toList();
    }

    if (Platform.isIOS) {
      selfieVideos =
          (map['livenessVideos'] as List?)?.map((dynamic e) => base64Decode(e)).toList();
    }
  }
}
