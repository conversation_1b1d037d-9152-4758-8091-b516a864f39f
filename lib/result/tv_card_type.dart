import 'package:trust_vision_plugin/result/tv_qr_sdk.dart';

enum TVCardOrientation {
  VERTICAL('vertical'),
  HORIZONTAL('horizontal');

  const TVCardOrientation(this.value);

  final String value;

  static TVCardOrientation? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'horizontal':
        return TVCardOrientation.HORIZONTAL;
      case 'vertical':
        return TVCardOrientation.VERTICAL;
      default:
        return null;
    }
  }
}

class TVCardType {
  String? id;
  String? name;
  bool? hasBackSide;
  TVCardOrientation? orientation;
  TVQrSdk? frontQr;

  TVCardType({
    required this.id,
    required this.name,
    required this.hasBackSide,
    required this.orientation,
    this.frontQr,
  });

  TVCardType.fromMap(Map<Object?, dynamic> map) {
    id = map['id'];
    name = map['name'];
    orientation = TVCardOrientation.fromString(map['orientation']);
    hasBackSide = map['hasBackSide'];
    frontQr = (map['frontQr'] as Map<Object?, dynamic>?) != null
        ? TVQrSdk.fromMap(map['frontQr'] as Map<Object?, dynamic>)
        : null;
  }

  Map<String, dynamic> toMap() {
    var map = <String, dynamic>{
      'id': id,
      'name': name,
      'orientation': orientation?.value.toString(),
      'hasBackSide': hasBackSide,
    };

    if (frontQr != null) {
      map['frontQr'] = frontQr?.toMap();
    }

    return map;
  }
}
