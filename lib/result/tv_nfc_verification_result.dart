import 'package:trust_vision_plugin/result/tv_nfc_verification_status.dart';

class TVNfcVerificationResult {
  String? verdict;
  TVNfcVerificationStatus? cloneStatus;
  TVNfcVerificationStatus? integrityStatus;
  TVNfcVerificationStatus? bcaStatus;

  TVNfcVerificationResult({
    this.verdict,
    this.cloneStatus,
    this.integrityStatus,
    this.bcaStatus,
  });

  TVNfcVerificationResult.fromMap(Map<Object?, dynamic> map) {
    verdict = map['verdict'];
    cloneStatus = (map['cloneStatus'] as Map<Object?, dynamic>?) != null
        ? TVNfcVerificationStatus.fromMap(map['cloneStatus'] as Map<Object?, dynamic>)
        : null;

    integrityStatus = (map['integrityStatus'] as Map<Object?, dynamic>?) != null
        ? TVNfcVerificationStatus.fromMap(map['integrityStatus'] as Map<Object?, dynamic>)
        : null;

    bcaStatus = (map['bcaStatus'] as Map<Object?, dynamic>?) != null
        ? TVNfcVerificationStatus.fromMap(map['bcaStatus'] as Map<Object?, dynamic>)
        : null;
  }
}
