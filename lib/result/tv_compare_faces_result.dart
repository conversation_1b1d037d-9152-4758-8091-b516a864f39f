enum MatchResult {
  MATCHED('matched'),
  UNMATCHED('unmatched'),
  UNSURE('unsure');

  const MatchResult(this.value);

  final String value;

  static MatchResult? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'matched':
        return MatchResult.MATCHED;
      case 'unmatched':
        return MatchResult.UNMATCHED;
      case 'unsure':
        return MatchResult.UNSURE;
      default:
        return null;
    }
  }
}

class TVCompareFacesResult {
  double? score;
  MatchResult? matchResult;
  String? requestId;
  String? timestamp;

  TVCompareFacesResult({required this.score, required this.matchResult, this.requestId, this.timestamp});

  TVCompareFacesResult.fromMap(Map<Object?, dynamic> map) {
    String? strScore = map['score']?.toString();
    score = strScore != null ? double.tryParse(strScore) : null;
    matchResult = MatchResult.fromString(map['matchResult']);
    requestId = map['requestId'];
    timestamp = map['timestamp'];
  }
}
