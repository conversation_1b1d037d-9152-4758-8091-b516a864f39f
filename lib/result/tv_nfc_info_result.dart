import 'package:trust_vision_plugin/result/tv_card_info_result.dart';
import 'package:trust_vision_plugin/result/tv_nfc_verification_result.dart';

class TVNfcInfoResult {
  String? requestId;
  String? com;
  String? sod;
  String? dg1;
  String? dg2;
  String? dg13;
  String? dg14;
  String? dg15;
  List<Info?>? infos;
  TVNfcVerificationResult? verificationResult;

  TVNfcInfoResult({
    this.requestId,
    this.com,
    this.sod,
    this.dg1,
    this.dg2,
    this.dg13,
    this.dg14,
    this.dg15,
    this.infos,
    this.verificationResult,
  });

  TVNfcInfoResult.fromMap(Map<Object?, dynamic> map) {
    requestId = map['requestId'];
    com = map['com'];
    sod = map['sod'];
    dg1 = map['dg1'];
    dg2 = map['dg2'];
    dg13 = map['dg13'];
    dg14 = map['dg14'];
    dg15 = map['dg15'];
    infos = (map['infos'] as List?)?.map((dynamic e) => Info.fromMap(e as Map<Object?, dynamic>)).toList();

    verificationResult = (map['verificationResult'] as Map<Object?, dynamic>?) != null
        ? TVNfcVerificationResult.fromMap(map['verificationResult'] as Map<Object?, dynamic>)
        : null;
  }
}
