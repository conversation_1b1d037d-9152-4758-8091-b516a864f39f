// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/widgets.dart';

import '../ui_model/error_ui_model.dart';
import 'ui_component_state.dart';

class UiComponentController {
  void Function()? loadData;
  void Function()? onDataCompleted;
  void Function(ErrorUIModel)? onError;
  void Function()? onEmptyData;

  UiComponentController({this.loadData, this.onDataCompleted, this.onError, this.onEmptyData});
}

/// Base class for UI Component. A UI Component contain 3 subcomponent:
/// #1: UI Widget extends Base UI component
/// #2: Cubit which communicate to Data layer
/// #3: State class work with Cubit
/// Note: expected as a UI Component should only have a API to get data.
/// [UiComponentController] is used by other class to control to get data and received [onComplete]
/// [onError] and [onEmptyData] event

abstract class UIComponentWidget extends StatefulWidget {
  final UiComponentController? controller;

  const UIComponentWidget({
    super.key,
    this.controller,
  });
}

abstract class UIComponentWidgetState<T extends UIComponentWidget> extends State<T> {
  void loadData();

  @override
  void initState() {
    super.initState();
    loadData();
    widget.controller?.loadData = loadData;
  }

  void handleCommonEvent(UiComponentState state) {
    if (state is UiComponentFailed) {
      widget.controller?.onError?.call(state.errorUIModel);
    }
    if (state is! UiComponentLoading) {
      widget.controller?.onDataCompleted?.call();
    }
  }
}
