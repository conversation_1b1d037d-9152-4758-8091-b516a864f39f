// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../init_common_package.dart';
import 'button_dimensions.dart';
import 'resources.dart';

class CommonButtonStyles {
  final CommonTextStyles textStyle = getIt.get<CommonTextStyles>();
  final CommonColors commonColors = getIt.get<CommonColors>();
  final CommonButtonDimensions buttonDimensions = getIt.get<CommonButtonDimensions>();

  ButtonStyle primary(
    ButtonSize size, {
    MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.padded,
  }) {
    return ButtonStyle(
      elevation: WidgetStateProperty.all(0),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            buttonDimensions.getCornerRadius(size),
          ),
        ),
      ),
      foregroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.primaryButtonForegroundDisable
              : commonColors.primaryButtonForeground;
        },
      ),
      backgroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.primaryButtonBgDisable
              : commonColors.primaryButtonBg;
        },
      ),
      padding: WidgetStateProperty.all(buttonDimensions.getPadding(size)),
      textStyle: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? textStyle.button(
                  size,
                  commonColors.primaryButtonForegroundDisable,
                  fontSize: buttonDimensions.getFontSize(size),
                )
              : textStyle.button(
                  size,
                  commonColors.primaryButtonForeground,
                  fontSize: buttonDimensions.getFontSize(size),
                );
        },
      ),
      tapTargetSize: tapTargetSize,
    );
  }

  ButtonStyle secondary(
    ButtonSize size, {
    MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.padded,
  }) {
    return ButtonStyle(
      elevation: WidgetStateProperty.all(0),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            buttonDimensions.getCornerRadius(size),
          ),
        ),
      ),
      foregroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.secondaryButtonForegroundDisable
              : commonColors.secondaryButtonForeground;
        },
      ),
      backgroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.secondaryButtonBgDisable
              : commonColors.secondaryButtonBg;
        },
      ),
      padding: WidgetStateProperty.all(buttonDimensions.getPadding(size)),
      textStyle: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? textStyle.button(
                  size,
                  commonColors.secondaryButtonForegroundDisable,
                  fontSize: buttonDimensions.getFontSize(size),
                )
              : textStyle.button(
                  size,
                  commonColors.secondaryButtonForeground,
                  fontSize: buttonDimensions.getFontSize(size),
                );
        },
      ),
      tapTargetSize: tapTargetSize,
    );
  }

  ButtonStyle tertiary(
    ButtonSize size, {
    bool isHasShadow = true,
    MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.padded,
  }) {
    return ButtonStyle(
      elevation: WidgetStateProperty.all(0),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            buttonDimensions.getCornerRadius(size),
          ),
        ),
      ),
      foregroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.tertiaryButtonForegroundDisable
              : commonColors.tertiaryButtonForeground;
        },
      ),
      backgroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.tertiaryButtonBgDisable
              : commonColors.tertiaryButtonBg;
        },
      ),
      padding: WidgetStateProperty.all(buttonDimensions.getPadding(size)),
      shadowColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return isHasShadow ? null : commonColors.tertiaryButtonBg;
        },
      ),
      textStyle: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? textStyle.button(
                  size,
                  commonColors.tertiaryButtonForegroundDisable,
                  fontSize: buttonDimensions.getFontSize(size),
                )
              : textStyle.button(
                  size,
                  commonColors.tertiaryButtonForeground,
                  fontSize: buttonDimensions.getFontSize(size),
                );
        },
      ),
      tapTargetSize: tapTargetSize,
    );
  }

  ButtonStyle accent(
    ButtonSize size, {
    MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.padded,
  }) {
    return ButtonStyle(
      elevation: WidgetStateProperty.all(0),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            buttonDimensions.getCornerRadius(size),
          ),
        ),
      ),
      foregroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.accentButtonForegroundDisable
              : commonColors.accentButtonForeground;
        },
      ),
      backgroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.accentButtonBgDisable
              : commonColors.accentButtonBg;
        },
      ),
      padding: WidgetStateProperty.all(buttonDimensions.getPadding(size)),
      textStyle: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? textStyle.button(
                  size,
                  commonColors.accentButtonForegroundDisable,
                  fontSize: buttonDimensions.getFontSize(size),
                )
              : textStyle.button(
                  size,
                  commonColors.accentButtonForeground,
                  fontSize: buttonDimensions.getFontSize(size),
                );
        },
      ),
      tapTargetSize: tapTargetSize,
    );
  }

  ButtonStyle negative(
    ButtonSize size, {
    MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.padded,
  }) {
    return ButtonStyle(
      elevation: WidgetStateProperty.all(0),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            buttonDimensions.getCornerRadius(size),
          ),
        ),
      ),
      foregroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.negativeButtonForegroundDisable
              : commonColors.negativeButtonForeground;
        },
      ),
      backgroundColor: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? commonColors.negativeButtonBgDisable
              : commonColors.negativeButtonBg;
        },
      ),
      padding: WidgetStateProperty.all(buttonDimensions.getPadding(size)),
      textStyle: WidgetStateProperty.resolveWith(
        (Set<WidgetState> states) {
          return states.contains(WidgetState.disabled)
              ? textStyle.button(
                  size,
                  commonColors.negativeButtonForegroundDisable,
                  fontSize: buttonDimensions.getFontSize(size),
                )
              : textStyle.button(
                  size,
                  commonColors.negativeButtonForeground,
                  fontSize: buttonDimensions.getFontSize(size),
                );
        },
      ),
      tapTargetSize: tapTargetSize,
    );
  }
}
