// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../data/response/base_entity.dart';

class ErrorUIModel {
  final int? statusCode;
  final String? verdict;
  final String? userMessage;
  final String? userMessageTitle;

  ErrorUIModel({
    this.statusCode,
    this.userMessage,
    this.verdict,
    this.userMessageTitle,
  });

  ErrorUIModel.fromEntity(BaseEntity? baseEntity)
      : statusCode = baseEntity?.statusCode,
        userMessage = baseEntity?.userMessage,
        verdict = baseEntity?.verdict,
        userMessageTitle = baseEntity?.userMessageTitle;
}
