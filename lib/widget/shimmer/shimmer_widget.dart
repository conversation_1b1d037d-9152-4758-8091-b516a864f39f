// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/widgets.dart';

import 'shimmer_painting.dart';

class ShimmerWidget extends StatefulWidget {
  final bool isLoading;
  final Widget skeletonWidget;
  final Widget child;
  final bool isPrimary;
  final int animationDurationInMilliSec;

  const ShimmerWidget({
    required this.isLoading,
    required this.skeletonWidget,
    required this.child,
    super.key,
    this.isPrimary = false,
    this.animationDurationInMilliSec = 300,
  });

  @override
  State<ShimmerWidget> createState() => _ShimmerWidgetState();
}

class _ShimmerWidgetState extends State<ShimmerWidget> with SingleTickerProviderStateMixin {
  bool isLoading = false;

  late AnimationController _opacityController;

  @override
  void initState() {
    super.initState();
    isLoading = widget.isLoading;
    _opacityController = AnimationController(
        vsync: this, duration: Duration(milliseconds: widget.animationDurationInMilliSec));
    _startChildWidgetAnimation();
  }

  @override
  void dispose() {
    _opacityController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant ShimmerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (isLoading != widget.isLoading) {
      isLoading = widget.isLoading;
      _startChildWidgetAnimation(isReset: true);
    }
  }

  void _startChildWidgetAnimation({bool isReset = false}) {
    if (isLoading) {
      return;
    }

    if (isReset) {
      // Reset animation of child widget
      _opacityController.reset();
    }
    _opacityController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? ShimmerPainting(child: widget.skeletonWidget)
        : FadeTransition(opacity: _opacityController, child: widget.child);
  }
}
