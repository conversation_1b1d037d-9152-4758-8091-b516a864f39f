// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../init_common_package.dart';
import '../resources/colors.dart';
import '../util/extension.dart';

class CommonLoadingView extends StatelessWidget {
  const CommonLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    return FractionallySizedBox(
        widthFactor: 1.0,
        heightFactor: 1.0,
        child: AbsorbPointer(
            child: Center(
                child: Wrap(children: <Widget>[
          SizedBox(
              height: context.screenHeight * 0.05,
              width: context.screenHeight * 0.05,
              child: CircularProgressIndicator(color: getIt.get<CommonColors>().loadingViewColor))
        ]))));
  }
}
