// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import 'common_dialog_bottom_sheet_cta.dart';

class CommonDialogBottomSheetCTAHorizontal extends CommonDialogBottomSheetCTA {
  final TextDirection textDirection;

  const CommonDialogBottomSheetCTAHorizontal({
    required super.ctaSpacing,
    required super.padding,
    required this.textDirection,

    /// Positive button
    required super.textPositive,
    super.onClickPositive,
    super.positiveButtonStyle,
    super.positiveDelayInSeconds,
    super.positiveDelayOverlay,

    /// Negative button
    super.textNegative,
    super.onClickNegative,
    super.negativeButtonStyle,
    super.key,
  });

  @override
  Widget getButtonWidgets() {
    final bool isShowNegative = textNegative?.isNotEmpty == true;
    final List<Widget> contents = <Widget>[];

    if (isShowNegative) {
      contents.addAll(
        <Widget>[
          Flexible(
            fit: FlexFit.tight,
            child: negativeButtonWidget(),
          ),
          SizedBox(width: ctaSpacing),
        ],
      );
    }

    contents.add(
      Flexible(
        fit: FlexFit.tight,
        child: positiveButtonWidget(),
      ),
    );

    return Row(
      textDirection: textDirection,
      children: contents,
    );
  }
}
