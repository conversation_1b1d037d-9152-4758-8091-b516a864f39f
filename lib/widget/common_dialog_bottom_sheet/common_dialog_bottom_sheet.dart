// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import '../../ui_model/dialog_type.dart';
import 'widgets/common_dialog_bottom_sheet_header.dart';

import '../../global_key_provider.dart';
import '../../util/extension.dart';
import '../common_base_dialog_widget.dart';
import 'widgets/common_dialog_bottom_sheet_content.dart';
import 'widgets/common_dialog_bottom_sheet_cta/common_dialog_bottom_sheet_cta.dart';

enum ButtonListOrientation {
  /// With [ButtonListOrientation.verticalDown], buttons are displayed vertically in a column,
  /// sorted from negativeButton to positiveButton.
  /// E.g: negativeButton
  ///             |
  ///      positiveButton
  verticalDown,

  /// With [ButtonListOrientation.verticalUp], buttons are displayed vertically in a column,
  /// sorted from positiveButton to negativeButton.
  /// E.g: positiveButton
  ///             |
  ///      negativeButton
  verticalUp,

  /// With [ButtonListOrientation.horizontalLeftToRight], buttons are displayed horizontally in a row,
  /// sorted from negativeButton to positiveButton. E.g: negativeButton - positiveButton
  horizontalLeftToRight,

  /// With [ButtonListOrientation.horizontalRightToLeft], buttons are displayed horizontally in a row,
  /// sorted from positiveButton to negativeButton. E.g: positiveButton - negativeButton
  horizontalRightToLeft,
}

class CommonDialogBottomSheet extends CommonBaseDialogWidget {
  static const EdgeInsets defaultHeaderPadding = EdgeInsets.only(
    top: 16,
    bottom: 40,
    left: 16,
    right: 16,
  );

  static const EdgeInsets defaultContentPadding = EdgeInsets.only(
    left: 16,
    right: 16,
    bottom: 16,
  );

  static const EdgeInsets defaultCTAPadding = EdgeInsets.only(
    left: 16,
    right: 16,
    bottom: 16,
  );

  static const double defaultContentSpacing = 16;

  final Widget? header;
  final String? title, content, textNegative;
  final String textPositive;
  final Widget? footer;
  final VoidCallback? onClickPositive, onClickNegative, onClickClose;
  final ButtonStyle? positiveButtonStyle;
  final ButtonStyle? negativeButtonStyle;
  final TextStyle? titleTextStyle;
  final TextStyle? contentTextStyle;
  final bool isShowButtonClose;
  final Widget? buttonClose;
  final TextAlign? titleTextAlign;
  final TextAlign? contentTextAlign;
  final ButtonListOrientation buttonListOrientation;
  final int? positiveDelayInSeconds;
  final Widget Function(int remainingTimeInSeconds)? positiveDelayOverlay;

  final EdgeInsets headerPadding;
  final EdgeInsets contentPadding;
  final EdgeInsets ctaPadding;
  final double contentSpacing;
  final double ctaSpacing;

  /// the reason why need to show this dialog
  /// added to metaData when send onShow log event
  final Map<String, dynamic>? loggingEventOnShowMetaData;

  const CommonDialogBottomSheet({
    required this.textPositive,
    required super.dialogId,
    super.key,
    this.title,
    super.eventTrackingScreenId,
    super.isEnableLoggingEvent = true,
    super.loggingEventMetaData,
    super.appEventId,
    this.loggingEventOnShowMetaData,
    super.dialogType = DialogType.bottomSheet,
    this.content,
    this.textNegative,
    this.onClickNegative,
    this.footer,
    this.onClickPositive,
    this.header,
    this.positiveButtonStyle,
    this.negativeButtonStyle,
    this.titleTextStyle,
    this.contentTextStyle,
    this.onClickClose,
    this.buttonClose,
    this.isShowButtonClose = false,
    this.titleTextAlign,
    this.contentTextAlign,
    this.buttonListOrientation = ButtonListOrientation.horizontalLeftToRight,
    EdgeInsets? headerPadding,
    EdgeInsets? contentPadding,
    EdgeInsets? ctaPadding,
    double? contentSpacing,
    double? ctaSpacing,
    this.positiveDelayInSeconds,
    this.positiveDelayOverlay,
  })  : headerPadding = headerPadding ?? defaultHeaderPadding,
        contentPadding = contentPadding ?? defaultContentPadding,
        ctaPadding = ctaPadding ?? defaultCTAPadding,
        contentSpacing = contentSpacing ?? defaultContentSpacing,
        ctaSpacing = ctaSpacing ??
            (buttonListOrientation == ButtonListOrientation.horizontalLeftToRight ||
                    buttonListOrientation == ButtonListOrientation.horizontalRightToLeft
                ? 16
                : 8);

  @override
  State<CommonDialogBottomSheet> createState() => CommonDialogBottomSheetState();
}

class CommonDialogBottomSheetState extends CommonBaseDialogWidgetState<CommonDialogBottomSheet> {
  @override
  Map<String, dynamic>? get loggingMetaDataUiContent => <String, dynamic>{
        'title': widget.title,
        'content': widget.content,
      };

  @override
  Map<String, dynamic>? get loggingEventOnShowMetaData => widget.loggingEventOnShowMetaData;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: context.screenPadding.bottom),
      child: Wrap(
        children: <Widget>[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _getContentWidgets(context),
          ),
        ],
      ),
    );
  }

  List<Widget> _getContentWidgets(BuildContext context) {
    final List<Widget> contents = <Widget>[];

    contents.add(_buildHeader());

    contents.add(_buildContent());

    contents.add(_buildCTA());

    return contents;
  }

  Widget _buildHeader() {
    final Widget? header = widget.header;
    if (header != null) {
      return CommonDialogSheetHeader(
        padding: widget.headerPadding,
        isShowButtonClose: widget.isShowButtonClose,
        buttonClose: widget.buttonClose,
        onClickClose: () {
          loggingOnCloseEvent();
          navigatorContext?.pop();
          widget.onClickClose?.call();
        },
        child: header,
      );
    } else {
      return SizedBox(height: widget.headerPadding.top);
    }
  }

  Widget _buildContent() {
    return CommonDialogBottomSheetContent(
      padding: widget.contentPadding,
      title: widget.title,
      content: widget.content,
      footer: widget.footer,
      titleTextStyle: widget.titleTextStyle,
      contentTextStyle: widget.contentTextStyle,
      titleTextAlign: widget.titleTextAlign,
      contentTextAlign: widget.contentTextAlign,
      contentSpacing: widget.contentSpacing,
    );
  }

  Widget _buildCTA() {
    final String textPositive = widget.textPositive;
    final String? textNegative = widget.textNegative;

    return CommonDialogBottomSheetCTA.create(
      buttonListOrientation: widget.buttonListOrientation,
      ctaSpacing: widget.ctaSpacing,
      padding: widget.ctaPadding,

      /// Positive button
      textPositive: textPositive,
      onClickPositive: () {
        loggingOnClickPositiveEvent(textPositive);
        widget.onClickPositive?.call();
      },
      positiveButtonStyle: widget.positiveButtonStyle,
      positiveDelayOverlay: widget.positiveDelayOverlay,
      positiveDelayInSeconds: widget.positiveDelayInSeconds,

      /// Negative button
      textNegative: textNegative,
      onClickNegative: () {
        loggingOnClickNegativeEvent(textNegative);
        widget.onClickNegative != null ? widget.onClickNegative?.call() : navigatorContext?.pop();
      },
      negativeButtonStyle: widget.negativeButtonStyle,
    );
  }
}
