// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

// ignore_for_file: avoid_catches_without_on_clauses

import 'package:disk_space_plus/disk_space_plus.dart';

import '../../../data/repository/logging/log_error_mixin.dart';
import '../model/storage_info.dart';
import 'storage_info_wrapper.dart';

class StorageInfoWrapperImpl extends StorageInfoWrapper with LogErrorMixin {
  @override
  Future<StorageInfo> getStorageInfo() async {
    double? freeStorageInMegabytes;
    double? totalStorageInMegabytes;
    try {
      freeStorageInMegabytes = await DiskSpacePlus.getFreeDiskSpace;
      totalStorageInMegabytes = await DiskSpacePlus.getTotalDiskSpace;
    } catch (err) {
      logPlatformErrorEvent(
        errorType: 'storage_info_wrapper',
        action: 'get_storage_info',
        error: err,
      );
    }

    return StorageInfo(
      freeStorageInMegabytes: freeStorageInMegabytes,
      totalStorageInMegabytes: totalStorageInMegabytes,
    );
  }
}
