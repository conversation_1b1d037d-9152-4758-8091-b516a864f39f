// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:in_app_update/in_app_update.dart';

import '../../init_common_package.dart';
import '../../util/device_platform.dart';
import '../../util/extension.dart';
import 'in_app_update_platform.dart';
import 'in_app_update_wrapper.dart';

class InAppUpdateWrapperImpl extends InAppUpdateWrapper {
  final InAppUpdatePlatform _inAppUpdatePlatform;
  final DevicePlatform platform = getIt.get<DevicePlatform>();

  // Constructor now accepts an InAppUpdatePlatform instance (for dependency injection)
  InAppUpdateWrapperImpl({InAppUpdatePlatform? inAppUpdatePlatform})
      : _inAppUpdatePlatform = inAppUpdatePlatform ?? DefaultInAppUpdatePlatform();

  @override
  Future<bool> isUpdateAvailable() async {
    if (platform.isIOS()) {
      return false;
    }

    try {
      final AppUpdateInfo updateInfo = await _inAppUpdatePlatform.checkForUpdate();
      return updateInfo.updateAvailability == UpdateAvailability.updateAvailable;
    } on Exception catch (e) {
      commonLog('Error checking for update: $e');
      return false;
    }
  }

  @override
  Future<AppUpdateResult> startImmediateUpdate() async {
    if (platform.isIOS()) {
      return AppUpdateResult.inAppUpdateFailed;
    }
    try {
      return await _inAppUpdatePlatform.performImmediateUpdate();
    } on Exception catch (e) {
      commonLog('Error starting immediate update: $e');
      return AppUpdateResult.inAppUpdateFailed;
    }
  }

  @override
  Future<AppUpdateResult> startFlexibleUpdate() async {
    if (platform.isIOS()) {
      return AppUpdateResult.inAppUpdateFailed;
    }
    try {
      return await _inAppUpdatePlatform.startFlexibleUpdate();
    } on Exception catch (e) {
      commonLog('Error starting flexible update: $e');
      return AppUpdateResult.inAppUpdateFailed;
    }
  }

  @override
  Stream<InstallStatus> installStateStream() {
    if (platform.isIOS()) {
      return Stream<InstallStatus>.empty();
    }
    return _inAppUpdatePlatform.installUpdateListener;
  }

  @override
  Future<void> completeFlexibleUpdate() async {
    if (platform.isIOS()) {
      return;
    }

    try {
      await _inAppUpdatePlatform.completeFlexibleUpdate();
    } on Exception catch (e) {
      commonLog('Error completing flexible update: $e');
    }
  }
}
