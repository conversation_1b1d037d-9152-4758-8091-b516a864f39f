// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../../init_common_package.dart';
import '../../resources/colors.dart';

class ProgressIndicatorController {
  void Function(double)? updateProgress;
  void Function(bool)? show;
}

class ProgressIndicatorWidget extends StatefulWidget {
  final ProgressIndicatorController? controller;
  final double initValue;
  final double? minHeight;
  final bool isVisible;

  const ProgressIndicatorWidget({
    super.key,
    this.controller,
    this.initValue = 0.0,
    this.minHeight,
    this.isVisible = true,
  });

  @override
  State<ProgressIndicatorWidget> createState() => _ProgressIndicatorWidgetState();
}

class _ProgressIndicatorWidgetState extends State<ProgressIndicatorWidget> {
  final double _progressIndicatorHeight = 2;
  final double _maxValueProgressIndicator = 1.0;

  late double _progressValue;
  late bool _visible;
  final CommonColors colors = getIt.get<CommonColors>();

  @override
  void initState() {
    super.initState();
    _progressValue = widget.initValue;
    _visible = widget.isVisible;
    widget.controller?.updateProgress = _updateProgress;
    widget.controller?.show = _setVisibility;
  }

  @override
  Widget build(BuildContext context) {
    return _visible
        ? LinearProgressIndicator(
            value: _progressValue,
            valueColor: AlwaysStoppedAnimation<Color>(colors.webViewProgressValue),
            backgroundColor: colors.webViewProgressBg,
            minHeight: widget.minHeight ?? _progressIndicatorHeight,
          )
        : const SizedBox();
  }

  void _updateProgress(double value) {
    setState(() {
      _progressValue = value;
      if (_progressValue >= _maxValueProgressIndicator) {
        _visible = false;
      }
    });
  }

  void _setVisibility(bool visibility) {
    setState(() {
      _visible = visibility;
    });
  }
}
