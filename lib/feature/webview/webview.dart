// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../../base/page_base.dart';
import '../../data/repository/logging/logging_repo.dart';
import '../../global_key_provider.dart';
import '../../init_common_package.dart';
import '../../resources/images.dart';
import '../../resources/resources.dart';
import '../../util/device_platform.dart';
import '../../util/extension.dart';
import '../../util/flutter_downloader/common_flutter_downloader.dart';
import '../../util/flutter_downloader/models/download_request.dart';
import '../../util/flutter_downloader/models/download_result.dart';
import '../../widget/common_app_bar.dart';
import '../../widget/common_image_provider.dart';
import '../../widget/default_widgets.dart';
import '../server_logging/event_tracking_screen_id.dart';
import '../server_logging/event_tracking_utils.dart';
import 'builder/webview_builder.dart';
import 'common_webview_controller.dart';
import 'in_app_webview_options/common_ios_in_app_webview_options.dart';
import 'progress_indicator_widget.dart';
import 'webview_constants.dart';
import 'webview_error_ui_model.dart';
import 'webview_utils.dart';

enum WebViewRedirectType { externalBrowser, inApp, cancel }

typedef WebViewAppStateChangeCallback = void Function(
  CommonWebViewController? controller,
  bool isTopVisible,
  AppLifecycleState state,
);

class CommonWebViewArg extends PageBaseArg {
  final String? title;
  final String? url;
  final String? defaultHtmlFileName;
  final bool isFullScreen;
  final WebViewRedirectType redirectType;
  final CommonWebViewController? controller;
  final PreferredSizeWidget? appBar;
  final bool useShouldOverrideUrlLoading;
  final bool safeAreaTop;
  final bool safeAreaBottom;
  final bool safeAreaLeft;
  final bool safeAreaRight;
  final Widget Function(String, VoidCallback)? errorWidget;
  final Widget? nextActionWidget;
  final CommonIOSInAppWebViewOptions? iosInAppWebViewOptions;
  final VoidCallback? onHandlePopScope;
  final VoidCallback? onDispose;
  final WebViewAppStateChangeCallback? onAppStateChange;

  /// Event Tracking args
  /// [eventTrackingMetaData] -> extra metadata, if host-app need to add some specific data
  final Map<String, dynamic>? Function(Uri? uri)? eventTrackingMetaData;

  /// [eventTrackingScreenId] -> default is [EventTrackingScreenId.webViewScreen],
  /// but sometime this webView can be a part of screen -> allow host-app to customize
  final EventTrackingScreenId eventTrackingScreenId;

  /// [appEventId] -> default is [EventTrackingUtils.defaultAppEventId],
  /// host-app can config if they want to use another appEventId
  final String appEventId;

  /// [isEnableEventTracking] -> host-app can config to disable/enable this logging event
  /// this webView will send on first loaded event
  /// eventId = [appEventId].[eventTrackingScreenId].[000]
  /// metadata = [loading_time] + eventTrackingMetaData from args
  final bool isEnableEventTracking;

  /// [onDidPopWebView] is used to listen event when the WebView is popped.
  /// If we listen to pop event from [dispose] method, this method will have a delay time
  /// So, we should listen pop event of WebView in [didPop] method when the WebView is popped.
  /// Refer: Fix bug for ticket https://trustingsocial1.atlassian.net/browse/EMA-3726
  final VoidCallback? onDidPopWebView;

  /// if navigation URL is match with regex [formatSpecialLinkHandledByHostApp] -> navigation action will be canceled and
  /// [onHandleSpecialLinkDetected] will be called
  final void Function(String)? onHandleSpecialLinkDetected;

  /// Regex for special link like deeplink, one link, or some link handled by host app
  /// Eg.r'evoappvn:\/\/mobile\/deeplinking'
  final RegExp? formatSpecialLinkHandledByHostApp;

  /// Controls whether the [Scaffold] resizes to avoid the bottom inset (e.g., keyboard).
  final bool? resizeToAvoidBottomInset;

  CommonWebViewArg({
    required this.title,
    required this.url,
    this.defaultHtmlFileName,
    this.isFullScreen = true,
    this.controller,
    this.errorWidget,
    this.appBar,
    this.useShouldOverrideUrlLoading = true,
    this.safeAreaTop = true,
    this.safeAreaBottom = true,
    this.safeAreaLeft = true,
    this.safeAreaRight = true,
    this.redirectType = WebViewRedirectType.externalBrowser,
    this.nextActionWidget,
    this.iosInAppWebViewOptions,
    this.onHandlePopScope,
    this.onDidPopWebView,
    this.onHandleSpecialLinkDetected,
    this.formatSpecialLinkHandledByHostApp,
    this.eventTrackingMetaData,
    this.eventTrackingScreenId = EventTrackingScreenId.webViewScreen,
    this.isEnableEventTracking = true,
    this.appEventId = EventTrackingUtils.defaultAppEventId,
    this.onAppStateChange,
    this.onDispose,
    this.resizeToAvoidBottomInset,
  });
}

class CommonWebView extends PageBase {
  static const String firstLoadedEventActionId = '000';
  static const String loadingTimeMetaDataKey = 'loading_time';
  static const String webLinkFullMetaDataKey = 'weblink_full';

  static void pushNamed({
    required CommonWebViewArg arg,
  }) {
    return navigatorContext?.pushNamed(
      CommonScreen.webViewPage.name,
      extra: CommonWebViewArg(
        title: arg.title,
        url: arg.url,
        defaultHtmlFileName: arg.defaultHtmlFileName,
        isFullScreen: arg.isFullScreen,
        controller: arg.controller,
        appBar: arg.appBar,
        useShouldOverrideUrlLoading: arg.useShouldOverrideUrlLoading,
        safeAreaTop: arg.safeAreaTop,
        safeAreaBottom: arg.safeAreaBottom,
        safeAreaLeft: arg.safeAreaLeft,
        safeAreaRight: arg.safeAreaRight,
        redirectType: arg.redirectType,
        nextActionWidget: arg.nextActionWidget,
        iosInAppWebViewOptions: arg.iosInAppWebViewOptions,
        onHandlePopScope: arg.onHandlePopScope,
        onDidPopWebView: arg.onDidPopWebView,
        formatSpecialLinkHandledByHostApp: arg.formatSpecialLinkHandledByHostApp,
        onHandleSpecialLinkDetected: arg.onHandleSpecialLinkDetected,
        errorWidget: (String error, VoidCallback onReload) =>
            getIt.get<CommonDefaultWidgets>().noInternetUI(error, onReload),
        eventTrackingScreenId: arg.eventTrackingScreenId,
        eventTrackingMetaData: arg.eventTrackingMetaData,
        isEnableEventTracking: arg.isEnableEventTracking,
        appEventId: arg.appEventId,
        onAppStateChange: arg.onAppStateChange,
        onDispose: arg.onDispose,
        resizeToAvoidBottomInset: arg.resizeToAvoidBottomInset,
      ),
    );
  }

  static void pushReplacementNamed({
    required CommonWebViewArg arg,
  }) {
    return navigatorContext?.pushReplacementNamed(
      CommonScreen.webViewPage.name,
      extra: CommonWebViewArg(
        title: arg.title,
        url: arg.url,
        defaultHtmlFileName: arg.defaultHtmlFileName,
        isFullScreen: arg.isFullScreen,
        controller: arg.controller,
        appBar: arg.appBar,
        useShouldOverrideUrlLoading: arg.useShouldOverrideUrlLoading,
        safeAreaTop: arg.safeAreaTop,
        safeAreaBottom: arg.safeAreaBottom,
        safeAreaLeft: arg.safeAreaLeft,
        safeAreaRight: arg.safeAreaRight,
        redirectType: arg.redirectType,
        nextActionWidget: arg.nextActionWidget,
        iosInAppWebViewOptions: arg.iosInAppWebViewOptions,
        onHandlePopScope: arg.onHandlePopScope,
        onDidPopWebView: arg.onDidPopWebView,
        formatSpecialLinkHandledByHostApp: arg.formatSpecialLinkHandledByHostApp,
        onHandleSpecialLinkDetected: arg.onHandleSpecialLinkDetected,
        errorWidget: (String error, VoidCallback onReload) =>
            getIt.get<CommonDefaultWidgets>().noInternetUI(error, onReload),
        eventTrackingScreenId: arg.eventTrackingScreenId,
        eventTrackingMetaData: arg.eventTrackingMetaData,
        isEnableEventTracking: arg.isEnableEventTracking,
        appEventId: arg.appEventId,
        onAppStateChange: arg.onAppStateChange,
        onDispose: arg.onDispose,
        resizeToAvoidBottomInset: arg.resizeToAvoidBottomInset,
      ),
    );
  }

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: CommonScreen.webViewPage.routeName,
        arguments: <String, dynamic>{'title': arg.title, 'url': arg.url},
      );

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  final CommonWebViewArg arg;

  @visibleForTesting
  final WebViewUiBuilder? webViewUiBuilder;

  const CommonWebView({
    required this.arg,
    super.key,
    @visibleForTesting this.webViewUiBuilder,
  });

  @override
  State<CommonWebView> createState() => CommonWebViewState<CommonWebView>();
}

class CommonWebViewState<T extends CommonWebView> extends PageStateBase<CommonWebView> {
  @visibleForTesting
  PullToRefreshController? pullToRefreshController;

  @visibleForTesting
  InAppWebViewController? webViewController;

  @visibleForTesting
  InAppWebViewSettings? settings;

  // initial value must not be 0.
  @visibleForTesting
  double webViewHeightSetInOnLoadStop = 1;

  @visibleForTesting
  bool hasError = false;

  @visibleForTesting
  WebViewErrorUIModel? webViewErrorUIModel;

  @visibleForTesting
  final ProgressIndicatorController progressIndicatorController = ProgressIndicatorController();

  @visibleForTesting
  late WebViewUiBuilder webViewUiBuilder;

  @visibleForTesting
  final DateTime initialTime = DateTime.now();

  @visibleForTesting
  bool isFirstTimeLoaded = false;

  // getter from GetIt
  @visibleForTesting
  final CommonColors colors = getIt.get<CommonColors>();

  @visibleForTesting
  final CommonFlutterDownloader flutterDownloaderWrapper = getIt.get<CommonFlutterDownloader>();

  @visibleForTesting
  final CommonWebViewUtils commonWebViewUtils = getIt.get<CommonWebViewUtils>();

  @visibleForTesting
  final bool isAndroid = getIt.get<DevicePlatform>().isAndroid();

  @override
  void initState() {
    webViewUiBuilder = widget.webViewUiBuilder ?? WebViewUiBuilder();
    commonLog(widget.arg.url);
    settings = InAppWebViewSettings(
      //Refer: https://trustingsocial1.atlassian.net/browse/EMA-1427
      useOnDownloadStart: shouldHandleDownloadManually(),
      useShouldOverrideUrlLoading: widget.arg.useShouldOverrideUrlLoading,
      mediaPlaybackRequiresUserGesture: false,
      transparentBackground: true,
      useHybridComposition: commonWebViewUtils.useHybridComposition,
      allowsInlineMediaPlayback: widget.arg.iosInAppWebViewOptions?.allowsInlineMediaPlayback ?? true,
    );

    setupPullToRequest();
    listenDownloadCallback();
    getCurrentUrl();

    super.initState();
  }

  // On Android, we need to handle the download manually.
  // On iOS, the WebView will handle the download and display the file if possible.
  @visibleForTesting
  bool shouldHandleDownloadManually() {
    return isAndroid;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    widget.arg.onAppStateChange?.call(widget.arg.controller, isTopVisible(), state);
    super.didChangeAppLifecycleState(state);
  }

  @visibleForTesting
  void setupPullToRequest() {
    pullToRefreshController = PullToRefreshController(
      settings: PullToRefreshSettings(color: colors.primary),
      onRefresh: () async {
        reloadWebView();
      },
    );
  }

  @visibleForTesting
  void listenDownloadCallback() {
    if (!shouldHandleDownloadManually()) {
      return;
    }

    flutterDownloaderWrapper.init();

    flutterDownloaderWrapper.listenDownloadCallback((DownloadResult result) {
      widget.arg.controller?.onDownloadCallback?.call(result);
      return;
    });
  }

  @override
  void dispose() {
    widget.arg.onDispose?.call();
    disposeFlutterDownloader();
    super.dispose();
  }

  @override
  void didPop() {
    widget.arg.onDidPopWebView?.call();
    super.didPop();
  }

  @visibleForTesting
  void disposeFlutterDownloader() {
    if (shouldHandleDownloadManually()) {
      flutterDownloaderWrapper.dispose();
    }
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return SizedBox(
      height: widget.arg.isFullScreen ? double.infinity : webViewHeightSetInOnLoadStop,
      child: (widget.arg.onHandlePopScope != null && isAndroid)
          ? PopScope(
              canPop: false,
              onPopInvokedWithResult: (bool didPop, _) {
                if (didPop) {
                  return;
                }

                widget.arg.onHandlePopScope?.call();
              },
              child: bodyWebView(),
            )
          : bodyWebView(),
    );
  }

  @visibleForTesting
  Widget bodyWebView() {
    return Scaffold(
      appBar: isShowAppBar()
          ? (setAppBar() ??
              CommonAppBar(title: widget.arg.title, actions: <Widget>[refreshWidget()]))
          : null,
      backgroundColor: widget.arg.isFullScreen ? colors.background : Colors.transparent,
      //on iOS, the resizeToAvoidBottomInset true making the animation when keyboard push up not smooth in some website, so default in iOS is false
      resizeToAvoidBottomInset: widget.arg.resizeToAvoidBottomInset ?? isAndroid,
      body: widget.arg.url == null
          ? const SizedBox.shrink()
          : SafeArea(
              top: widget.arg.safeAreaTop,
              bottom: widget.arg.safeAreaBottom,
              left: widget.arg.safeAreaLeft,
              right: widget.arg.safeAreaRight,
              //use MediaQuery.viewPaddingOf instead of MediaQuery.padding for bottom area
              //because MediaQuery.padding size changing when keyboard toggle make UI not smooth
              //to understand Padding, ViewPadding, and ViewInsets, check https://www.youtube.com/watch?v=ceCo8U0XHqw
              maintainBottomViewPadding: true,
              child: Column(
                children: <Widget>[
                  Expanded(
                    child: Column(
                      children: <Widget>[
                        ProgressIndicatorWidget(
                          controller: progressIndicatorController,
                          isVisible: widget.arg.isFullScreen,
                        ),
                        Expanded(child: Stack(children: <Widget>[getWebView(), errorWidget()])),
                      ],
                    ),
                  ),
                  nextActionWidget(),
                ],
              ),
            ),
    );
  }

  @visibleForTesting
  bool isShowAppBar() => widget.arg.isFullScreen;

  @visibleForTesting
  PreferredSizeWidget? setAppBar() => widget.arg.appBar;

  @visibleForTesting
  bool isShowErrorView() => widget.arg.isFullScreen && hasError;

  @visibleForTesting
  Widget errorWidget() {
    return webViewUiBuilder.errorWidget(
      isShowErrorView: isShowErrorView(),
      errorWidget: widget.arg.errorWidget?.call(webViewErrorUIModel?.type.userMessage ?? '', () {
        reloadWebView();
      }),
    );
  }

  @visibleForTesting
  Widget nextActionWidget() {
    // Refer: https://trustingsocial1.atlassian.net/browse/EMA-596
    return webViewUiBuilder.nextActionWidget(
      isShowNextActionWidget: !hasError,
      nextActionWidget: widget.arg.nextActionWidget,
    );
  }

  @visibleForTesting
  Widget getWebView() => InAppWebView(
        initialSettings: settings,
        pullToRefreshController: pullToRefreshController,
        initialUrlRequest: URLRequest(url: WebUri(widget.arg.url!)),
        onWebViewCreated: onWebViewCreated,
        onPermissionRequest: onPermissionRequest,
        onLoadStop: onLoadStop,
        //called when there are issues with loading a resource due to problems like network connectivity or DNS resolution failures
        onReceivedError: onReceivedError,
        //notify the host application that an HTTP error has been received from the server while loading a resource
        onReceivedHttpError: onReceivedHttpError,
        shouldOverrideUrlLoading: shouldOverrideUrlLoading,
        onReceivedServerTrustAuthRequest: onReceivedServerTrustAuthRequest,
        onProgressChanged: handleProgressChanged,
        onDownloadStartRequest: onDownloadStartRequest,
      );

  @visibleForTesting
  Future<ServerTrustAuthResponse?> onReceivedServerTrustAuthRequest(
    InAppWebViewController controller,
    URLAuthenticationChallenge challenge,
  ) async {
    //  only allow showing trusted url
    return ServerTrustAuthResponse(action: ServerTrustAuthResponseAction.PROCEED);
  }

  @visibleForTesting
  Future<NavigationActionPolicy?> shouldOverrideUrlLoading(
    InAppWebViewController controller,
    NavigationAction navigationAction,
  ) async {
    final URLRequest urlRequest = navigationAction.request;
    final Uri? uri = urlRequest.url;
    commonLog('#shouldOverrideUrlLoading url=$uri');
    commonLog('#shouldOverrideUrlLoading navigationAction=${navigationAction.toString()}');

    final NavigationActionPolicy navigationActionPolicy =
        await commonWebViewUtils.getNavigationActionPolicy(
      navigationAction: navigationAction,
      originalUrl: widget.arg.url!,
      redirectType: widget.arg.redirectType,
      formatSpecialLink: widget.arg.formatSpecialLinkHandledByHostApp,
      onHandleSpecialLink: widget.arg.onHandleSpecialLinkDetected,
    );
    widget.arg.controller?.onRedirectUrl?.call(uri, navigationActionPolicy);
    return navigationActionPolicy;
  }

  @visibleForTesting
  void onReceivedHttpError(
    InAppWebViewController controller,
    WebResourceRequest request,
    WebResourceResponse errorResponse,
  ) {
    commonLog(
        '#onReceivedHttpError url=${request.url}, code=${errorResponse.statusCode}, message=${errorResponse.reasonPhrase}, isForMainFrame = ${request.isForMainFrame}');
    handleLoadingError(
      type: WebViewErrorUIType.httpError,
      isForMainFrame: request.isForMainFrame ?? false,
      url: request.url,
      statusCode: errorResponse.statusCode,
      description: errorResponse.reasonPhrase,
    );
  }

  @visibleForTesting
  void onReceivedError(
    InAppWebViewController controller,
    WebResourceRequest request,
    WebResourceError error,
  ) {
    //the old onLoadError method is deprecated, the new method does not directly expose the statusCode any more
    //the statusCode is mapped from error type from: https://github.com/pichillilorenzo/flutter_inappwebview/blob/0aaf7a0bfc01d61a4d1453cefb57fb6783b6e676/flutter_inappwebview_android/lib/src/in_app_webview/in_app_webview_controller.dart#L241
    final int statusCode = error.type.toNativeValue() ?? -1;
    commonLog(
        '#onReceivedError url=${request.url}, code=$statusCode, message=${error.description}, isForMainFrame = ${request.isForMainFrame}');

    /// A request has been canceled by user
    /// We should not handle as an error here
    ///
    ///**Officially Supported Platforms/Implementations**:
    ///- iOS ([Official API - URLError.cancelled](https://developer.apple.com/documentation/foundation/urlerror/code/2883178-cancelled))
    if (error.type == WebResourceErrorType.CANCELLED) {
      return;
    }

    handleLoadingError(
      type: WebViewErrorUIType.networkError,
      isForMainFrame: request.isForMainFrame ?? false,
      url: request.url,
      statusCode: statusCode,
      description: error.description,
    );
  }

  @visibleForTesting
  Future<void> onLoadStop(InAppWebViewController controller, Uri? url) async {
    commonLog('WebView: onLoadStop ${DateTime.now()}');
    // according to docs, evaluate js code should be called inside onLoadStop
    // https://inappwebview.dev/docs/webview/javascript/injection
    handleJavascriptEvaluatorsOnLoadStop();

    logEventFirstTimeLoaded(url, widget.arg);
    widget.arg.controller?.onLoaded?.call();
    pullToRefreshController?.endRefreshing();

    if (!widget.arg.isFullScreen) {
      final dynamic height = await controller.evaluateJavascript(
        source: 'document.documentElement.scrollHeight;',
      );
      commonLog('document.documentElement.scrollHeight $height');
      if (height != null) {
        setState(() {
          if (height is String) {
            webViewHeightSetInOnLoadStop = double.parse(height);
          } else if (height is num) {
            webViewHeightSetInOnLoadStop = height.toDouble() + 5;
          }
        });
      }
    } else {
      if (webViewErrorUIModel == null && hasError) {
        setState(() {
          hasError = false;
        });
      }
    }
  }

  @visibleForTesting
  Future<PermissionResponse?> onPermissionRequest(
    InAppWebViewController controller,
    PermissionRequest request,
  ) async {
    commonLog(
      'androidOnPermissionRequest: origin = ${request.origin}, resources = ${request.resources}',
    );
    return handleRequestPermission(resources: request.resources);
  }

  @visibleForTesting
  void onWebViewCreated(InAppWebViewController controller) {
    commonLog('onWebViewCreated');
    webViewController = controller;
    initializeWebViewController();
  }

  @visibleForTesting
  Future<void> onDownloadStartRequest(
    InAppWebViewController controller,
    DownloadStartRequest uri,
  ) async {
    final DownloadRequest request = DownloadRequest(
      url: uri.url.toString(),
      suggestedFilename: uri.suggestedFilename,
      hasOpenFile: true,
    );

    await flutterDownloaderWrapper.requestDownloadFile(request: request);
  }

  @visibleForTesting
  void handleLoadingError({
    required WebViewErrorUIType type,
    required bool isForMainFrame,
    Uri? url,
    int? statusCode,
    String? description,
  }) {
    pullToRefreshController?.endRefreshing();
    //logging event
    getIt.get<LoggingRepo>().logErrorEvent(errorType: 'web_view', args: <String, dynamic>{
      'url': url?.origin,
      'statusCode': statusCode,
      'description': description,
      'isForMainFrame': isForMainFrame,
    });
    //only show the error if it's the request for main frame
    //for instance, a 404 error on a missing image might be handled differently than a 404 error on the main document.
    //current behavior:
    //for mainframe error request: error callback trigger for both Android and iOS
    //for non-mainframe error request: error callback only trigger in Android
    if (!isForMainFrame) return;
    if (widget.arg.defaultHtmlFileName?.isNotEmpty == true) {
      webViewController?.loadFile(assetFilePath: widget.arg.defaultHtmlFileName!);
    } else {
      if (widget.arg.isFullScreen) {
        webViewErrorUIModel = WebViewErrorUIModel(type: type, code: statusCode);

        widget.arg.controller?.onLoadError?.call(webViewErrorUIModel);

        /// Just show error message when host app do not setup error widget
        if (widget.arg.errorWidget == null) {
          if (type == WebViewErrorUIType.networkError) {
            showToast(type.userMessage);
          } else if (type == WebViewErrorUIType.httpError) {
            handleApiCommonError(webViewErrorUIModel);
          }
        }

        if (!hasError) {
          setState(() {
            hasError = true;
          });
        }
      }
    }
  }

  @visibleForTesting
  Future<PermissionResponse> handleRequestPermission({
    required List<PermissionResourceType> resources,
  }) async {
    /// This is Permission Request for camera on Android
    /// Refer: https://developer.android.com/reference/android/webkit/PermissionRequest#summary
    //PermissionResourceType.CAMERA is android.webkit.resource.VIDEO_CAPTURE in Android when we define into the source code
    final bool isRequireCameraPermission = resources.contains(PermissionResourceType.CAMERA);
    if (!isRequireCameraPermission) {
      return PermissionResponse(resources: resources, action: PermissionResponseAction.GRANT);
    }

    final bool? isGrant = await widget.arg.controller?.checkAndroidCameraPermission?.call();
    if (isGrant == true) {
      return PermissionResponse(resources: resources, action: PermissionResponseAction.GRANT);
    }

    return PermissionResponse(resources: resources);
  }

  @visibleForTesting
  void handleProgressChanged(InAppWebViewController controller, int value) {
    if (widget.arg.isFullScreen) {
      progressIndicatorController.updateProgress?.call(value / 100);
    }
  }

  @visibleForTesting
  Widget refreshWidget() {
    return Padding(
        padding: const EdgeInsets.only(right: 8),
        child: Tooltip(
            message: CommonStrings.refresh,
            child: InkWell(
                customBorder: const CircleBorder(),
                onTap: () {
                  reloadWebView();
                },
                child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: getIt.get<CommonImageProvider>().asset(CommonImages.icRefresh,
                        height: 24, width: 24, package: FLUTTER_COMMON_PACKAGE)))));
  }

  @visibleForTesting
  Future<void> reloadWebView() async {
    webViewErrorUIModel = null;

    await webViewController?.stopLoading();

    final WebUri? webUri = await webViewController?.getUrl();
    final String? webUriValue = webUri?.uriValue.toString();

    /// In case lost internet before go into web view on iOS, the current URL is empty.
    /// Check if the current URL is empty, then reload the initial URL
    if (webUriValue?.isNotEmpty == true) {
      webViewController?.reload();
    } else {
      final String? url = widget.arg.url;
      if (url == null) {
        return;
      }
      webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
    }

    if (widget.arg.isFullScreen) {
      progressIndicatorController.show?.call(true);
    }
  }

  @visibleForTesting
  void initializeWebViewController() {
    widget.arg.controller?.reload = () {
      reloadWebView();
    };

    widget.arg.controller?.canGoBack = () async {
      return await webViewController?.canGoBack() ?? false;
    };

    widget.arg.controller?.goBack = () async {
      await webViewController?.goBack();
    };

    widget.arg.controller?.hideKeyboard = () async {
      await webViewController?.evaluateJavascript(
          source: WebViewConstants.javaScriptToHideKeyboard);
    };

    /// handle calls from webpage's javascript to app
    handleJavascriptCallsFromWebView();
  }

  @visibleForTesting
  void handleJavascriptCallsFromWebView() {
    final List<JavaScriptHandler>? javaScriptHandlers = widget.arg.controller?.javaScriptHandlers;
    if (javaScriptHandlers != null) {
      for (final JavaScriptHandler handler in javaScriptHandlers) {
        webViewController?.addJavaScriptHandler(
            handlerName: handler.handlerName, callback: handler.callback);
      }
    }
  }

  @visibleForTesting
  Future<void> handleJavascriptEvaluatorsOnLoadStop() async {
    commonLog('#handleJavascriptEvaluatorsOnLoaded');
    final List<JavaScriptEvaluator>? evaluators = widget.arg.controller?.javaScriptEvaluators;
    if (evaluators == null) return;
    for (final JavaScriptEvaluator evaluator in evaluators) {
      commonLog('WebView: start evaluateJavascript ${evaluator.source}');
      final dynamic result = await webViewController?.evaluateJavascript(
        source: evaluator.source,
      );
      commonLog('WebView: end evaluateJavascript result=$result');
      evaluator.onResult?.call(result);
    }
  }

  @visibleForTesting
  void getCurrentUrl() {
    widget.arg.controller?.getCurrentUrl = () async {
      return await webViewController?.getUrl();
    };
  }

  @visibleForTesting
  void logEventFirstTimeLoaded(Uri? uri, CommonWebViewArg arg) {
    // check flag to make sure that only log for the first time loaded
    if (isFirstTimeLoaded) {
      return;
    }
    final Duration duration = DateTime.now().difference(initialTime);
    isFirstTimeLoaded = true;
    if (arg.isEnableEventTracking == false) {
      commonLog('Skip call logging event because you disable it');
      return;
    }
    logEventWebViewFirstTimeLoaded(uri, duration, arg);
  }

  @visibleForTesting
  void logEventWebViewFirstTimeLoaded(Uri? uri, Duration duration, CommonWebViewArg arg) {
    final EventTrackingUtils eventTrackingUtils = getIt<EventTrackingUtils>();
    final String eventId = EventTrackingUtils.buildEventId(
      screenNumberId: arg.eventTrackingScreenId.name,
      eventActionId: CommonWebView.firstLoadedEventActionId,
      appEventId: arg.appEventId,
    );
    final Map<String, dynamic> metaData = <String, dynamic>{
      ...?arg.eventTrackingMetaData?.call(uri),
      CommonWebView.loadingTimeMetaDataKey: duration.inMilliseconds,
      CommonWebView.webLinkFullMetaDataKey: uri?.toString(),
    };
    eventTrackingUtils.sendUserActionEvent(
      eventId: eventId,
      metaData: metaData,
    );
  }
}
