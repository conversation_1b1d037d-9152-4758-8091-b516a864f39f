// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:ui';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../../util/flutter_downloader/models/download_result.dart';
import 'webview_error_ui_model.dart';

class JavaScriptHandler {
  String handlerName;
  dynamic Function(List<dynamic> arguments) callback;

  /// write a constructor that takes a callback and a handlerName
  JavaScriptHandler({
    required this.callback,
    required this.handlerName,
  });
}

class JavaScriptEvaluator {
  final String source;
  final dynamic Function(dynamic result)? onResult;

  const JavaScriptEvaluator({
    required this.source,
    this.onResult,
  });
}

class CommonWebViewController {
  void Function()? reload;
  VoidCallback? onLoaded;
  void Function(WebViewErrorUIModel?)? onLoadError;
  Future<bool> Function()? canGoBack;
  Future<void> Function()? goBack;
  void Function(Uri?, NavigationActionPolicy)? onRedirectUrl;
  void Function(DownloadResult)? onDownloadCallback;
  void Function()? hideKeyboard;
  Future<Uri?> Function()? getCurrentUrl;

  /// List of JavaScriptHandler to handle calls from webpage's javascript to app
  List<JavaScriptHandler>? javaScriptHandlers;

  /// List of JavaScript sources to evaluate in WebView
  /// this will be called inside onLoadStop
  List<JavaScriptEvaluator>? javaScriptEvaluators;

  /// On Android, Check & request camera permission for WebView
  /// If the user denies camera permission, DOP WebView will show an instruction screen that guides customers.
  Future<bool> Function()? checkAndroidCameraPermission;

  CommonWebViewController({
    this.reload,
    this.onLoaded,
    this.onLoadError,
    this.canGoBack,
    this.goBack,
    this.onRedirectUrl,
    this.onDownloadCallback,
    this.hideKeyboard,
    this.checkAndroidCameraPermission,
    this.javaScriptHandlers,
    this.javaScriptEvaluators,
    this.getCurrentUrl,
  });
}
