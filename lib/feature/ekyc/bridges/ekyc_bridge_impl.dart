// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:trust_vision_plugin/enums.dart';
import 'package:trust_vision_plugin/result/selfie_image.dart';
import 'package:trust_vision_plugin/result/tv_detection_result.dart';
import 'package:trust_vision_plugin/result/tv_frame_batch.dart';
import 'package:trust_vision_plugin/result/tv_frame_class.dart';
import 'package:trust_vision_plugin/trust_vision_plugin.dart';

import '../../../data/repository/logging/logging_repo.dart';
import '../../../util/extension.dart';
import 'ekyc_bridge.dart';
import 'models/ekyc_bridge_error_reason.dart';
import 'models/ekyc_bridge_frame.dart';
import 'models/ekyc_bridge_frame_batch.dart';
import 'models/ekyc_bridge_image.dart';
import 'models/ekyc_bridge_liveness_mode.dart';
import 'models/ekyc_bridge_result.dart';
import 'models/ekyc_bridge_selfie_capturing_result.dart';
import 'models/ekyc_bridge_selfie_config.dart';
import 'utils/ekyc_bridge_selfie_utils.dart';

enum EkycStepStatus {
  start,
  succeed,
  failed;
}

enum EkycBridgeStep {
  initEkycSdk('init_ekyc_sdk'),
  selfieCapturing('selfie_capturing');

  final String value;

  const EkycBridgeStep(this.value);
}

class EkycBridgeImpl implements EkycBridge {
  final LoggingRepo _loggingRepo;
  final TrustVisionPlugin _trustVisionPlugin;

  EkycBridgeImpl({
    required TrustVisionPlugin trustVisionPlugin,
    required LoggingRepo loggingRepo,
  })  : _trustVisionPlugin = trustVisionPlugin,
        _loggingRepo = loggingRepo;

  @override
  Future<EkycBridgeResult<void>> initEkyc({
    required String? jsonConfigurationByServer,
    String? languageCode,
  }) async {
    const EkycBridgeStep step = EkycBridgeStep.initEkycSdk;
    logEvent(step: step, stepStatus: EkycStepStatus.start);
    try {
      await _trustVisionPlugin.initialize(
        jsonConfigurationByServer: jsonConfigurationByServer,
        languageCode: languageCode,
      );
    } on PlatformException catch (exception) {
      commonLog('PlatformException $exception');
      return handlePlatformException(step, exception);
      // ignore: avoid_catches_without_on_clauses
    } catch (exception) {
      commonLog('OtherException $exception');
      return handleFailed(
        step: step,
        errorReason: EkycBridgeErrorReason.unknown,
        errorDetail: exception.toString(),
      );
    }

    return handleSucceed<void>(step, null);
  }

  @override
  Future<EkycBridgeResult<EkycBridgeSelfieCapturingResult>> startSelfieCapturing({
    required EkycBridgeLivenessMode livenessMode,
    required OnNewFrameBatchListener onNewFrameBatchListener,
    bool skipConfirmScreen = false,
  }) async {
    const EkycBridgeStep step = EkycBridgeStep.selfieCapturing;
    logEvent(step: step, stepStatus: EkycStepStatus.start);

    List<EkycBridgeImage> images = <EkycBridgeImage>[];
    List<String> validFrameBatchIds = <String>[];

    try {
      final Map<String, dynamic> config = EkycBridgeSelfieConfig.verificationFlow(
        livenessMode,
      ).toMap();
      final TVDetectionResult? selfieCapturingResult = await _trustVisionPlugin.captureSelfie(
        config,
        onNewFrameBatch: (TvFrameBatch frameBatch) {
          /// Refer: https://ekyc.trustingsocial.com/sdks/Android-SDK#303-handle-onnewframebatch-callback
          handleOnNewFrameBatch(frameBatch, onNewFrameBatchListener);
        },
      );

      validFrameBatchIds = selfieCapturingResult?.livenessFrameBatchIds ?? <String>[];
      images = EkycBridgeSelfieImageUtils().convertTVSelfieImageToBridgeImage(
        livenessMode: livenessMode,
        images: selfieCapturingResult?.selfieImages ?? <SelfieImage>[],
      );
    } on PlatformException catch (exception) {
      commonLog('PlatformException $exception');
      return handlePlatformException(step, exception);
      // ignore: avoid_catches_without_on_clauses
    } catch (exception) {
      commonLog('OtherException $exception');
      return handleFailed(
        step: step,
        errorReason: EkycBridgeErrorReason.unknown,
        errorDetail: exception.toString(),
      );
    }

    return handleSucceed(
      step,
      EkycBridgeSelfieCapturingResult(
        images: images,
        validFrameBatchIds: validFrameBatchIds,
      ),
    );
  }

  @visibleForTesting
  void handleOnNewFrameBatch(
    TvFrameBatch frameBatch,
    OnNewFrameBatchListener onNewFrameBatchListener,
  ) {
    final List<EkycBridgeFrame>? frames = frameBatch.frames
        ?.map(
          (TvFrameClass frameClass) => EkycBridgeFrame(
            base64: frameClass.base64,
            index: frameClass.index,
            label: frameClass.label,
            metadata: frameClass.metadata,
          ),
        )
        .toList();
    onNewFrameBatchListener(
      EkycBridgeFrameBatch(
        id: frameBatch.id,
        frames: frames,
        metadata: frameBatch.metadata,
        label: 'video',
      ),
    );
  }

  @visibleForTesting
  EkycBridgeResult<T> handleSucceed<T>(EkycBridgeStep step, T data) {
    logEvent(step: step, stepStatus: EkycStepStatus.succeed);
    return EkycBridgeResult<T>.succeed(data: data);
  }

  @visibleForTesting
  EkycBridgeResult<T> handlePlatformException<T>(
    EkycBridgeStep step,
    PlatformException exception,
  ) {
    EkycBridgeErrorReason errorReason = EkycBridgeErrorReason.unknown;
    if (exception.code == TVErrorCode.sdk_canceled.name) {
      errorReason = EkycBridgeErrorReason.userCancelled;
    }

    if (exception.code == TVErrorCode.access_denied_exception.name) {
      errorReason = EkycBridgeErrorReason.sessionExpired;
    }
    return handleFailed(
      step: step,
      errorReason: errorReason,
      errorDetail: exception.toString(),
    );
  }

  @visibleForTesting
  EkycBridgeResult<T> handleFailed<T>({
    required EkycBridgeStep step,
    required EkycBridgeErrorReason errorReason,
    String? errorDetail,
  }) {
    logEvent(
      step: step,
      stepStatus: EkycStepStatus.failed,
      errorCode: errorReason.value,
      errorDetail: errorDetail,
    );
    return EkycBridgeResult<T>.failed(errorReason: errorReason);
  }

  @visibleForTesting
  void logEvent({
    required EkycBridgeStep step,
    required EkycStepStatus stepStatus,
    String? errorCode,
    String? errorDetail,
    String? value,
  }) {
    final Map<String, dynamic> data = <String, dynamic>{
      'step': step.value,
      'stepStatus': stepStatus.name,
    };
    if (errorCode != null) {
      data['errorCode'] = errorCode;
    }
    if (errorDetail != null) {
      data['errorDetail'] = errorDetail;
    }
    if (value != null) {
      data['value'] = value;
    }
    _loggingRepo.logEvent(eventType: EventType.ekyc, data: data);
  }
}
