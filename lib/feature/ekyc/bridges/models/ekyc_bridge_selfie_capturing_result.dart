// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'ekyc_bridge_image.dart';

class EkycBridgeSelfieCapturingResult {
  final List<EkycBridgeImage> images;
  final List<String> validFrameBatchIds;

  const EkycBridgeSelfieCapturingResult({
    required this.images,
    required this.validFrameBatchIds,
  });
}
