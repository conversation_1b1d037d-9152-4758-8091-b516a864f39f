// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:trust_vision_plugin/result/tv_image_class.dart';

import 'ekyc_bridge_image_direction.dart';

class EkycBridgeImage {
  final String? rawImageBase64;
  final String? encryptedImageHexString;
  final String? imageId;
  final Map<String, dynamic>? metadata;
  final EkycBridgeImageDirection? direction;

  const EkycBridgeImage({
    this.rawImageBase64,
    this.encryptedImageHexString,
    this.imageId,
    this.metadata,
    this.direction,
  });

  static EkycBridgeImage fromTVImageClass({
    required TVImageClass tvImage,
    EkycBridgeImageDirection? direction,
  }) {
    return EkycBridgeImage(
      rawImageBase64: tvImage.rawImageBase64,
      encryptedImageHexString: tvImage.encryptedImageHexString,
      imageId: tvImage.imageId,
      metadata: tvImage.metadata,
      direction: direction,
    );
  }
}
