// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';

import '../../../data/http_client/common_http_client.dart';
import '../../../data/http_client/mock_config.dart';
import '../../../data/repository/ekyc/ekyc_repo.dart';
import '../../../data/request/ekyc_upload_frames_request.dart';
import '../../../data/request/ekyc_upload_images_request.dart';
import '../../../data/response/ekyc_client_settings_entity.dart';
import '../../../data/response/ekyc_upload_frames_entity.dart';
import '../../../data/response/ekyc_upload_images_entity.dart';
import '../../../init_common_package.dart';
import '../../../util/extension.dart';
import '../bridges/ekyc_bridge.dart';
import '../bridges/models/ekyc_bridge_frame_batch.dart';
import '../bridges/models/ekyc_bridge_image.dart';
import '../bridges/models/ekyc_bridge_liveness_mode.dart';
import '../bridges/models/ekyc_bridge_result.dart';
import '../bridges/models/ekyc_bridge_selfie_capturing_result.dart';
import 'mocks/mock_get_client_settings_case.dart';
import 'mocks/mock_upload_frames_case.dart';
import 'mocks/mock_upload_images_case.dart';
import 'models/facial_verification_initialize_args.dart';
import 'models/facial_verification_initialize_result.dart';
import 'models/facial_verification_start_capturing_args.dart';
import 'models/facial_verification_start_capturing_result.dart';

// For Mock API, add json mock file in host-app repo
// reason: if we add json mock file in this package, it will be included in the output APK file (security reason)
// refer: https://trustingsocial1.atlassian.net/browse/EMA-1065
class FacialVerificationHandler {
  static FacialVerificationHandler? _instance;

  static final FacialVerificationHandler _originalInstance = FacialVerificationHandler._internal();

  factory FacialVerificationHandler() {
    return _instance ??= _originalInstance;
  }

  FacialVerificationHandler._internal();

  // Method to replace the singleton instance (for testing only)
  @visibleForTesting
  static set instanceForTesting(FacialVerificationHandler instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  @visibleForTesting
  static void resetToOriginalInstance() {
    _instance = _originalInstance;
  }

  final EkycBridge ekycBridge = getIt<EkycBridge>();
  final EkycRepo ekycRepo = getIt<EkycRepo>();

  @visibleForTesting
  // flag to determine if SDK is initialized
  // this flag is used to check if SDK is ready to start capturing
  bool isInitialized = false;

  @visibleForTesting
  // session token to identify the current session
  // this token is used to communicate with the server (header)
  // can be null if user using bearer token instead of session token
  String? sessionToken;

  @visibleForTesting
  // list of completer to handle upload frame batch
  // will help to make sure that all frames are uploaded
  final List<Completer<void>> uploadFrameBatchCompleterList = <Completer<void>>[];

  @visibleForTesting
  // map to store frameBatchId (local) and uploadedFileId (server)
  // will help to remove invalid frameBatchId after capturing
  // refer: https://ekyc.trustingsocial.com/sdks/React-Native-SDK#3341-remove-invalid-frame-batch-ids
  Map<String, String> frameBatchIdUploadedFileIdMap = <String, String>{};

  // Method to initialize facial verification
  // this method will get client settings from the server and init SDK
  Future<FacialVerificationInitializeResult> initialize({
    required FacialVerificationInitializeArgs args,
  }) async {
    commonLog('initializing facial verification');
    // clear old data to avoid memory leak and data inconsistency
    clearSessionData();

    // set new session data from args
    sessionToken = args.sessionToken;

    commonLog('getting client settings from API');
    final EkycClientSettingsEntity response = await ekycRepo.getClientSettings(
      sessionToken: sessionToken,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockGetClientSettingsCaseFileName(MockGetClientSettingsCase.success),
      ),
    );

    if (response.statusCode != CommonHttpClient.SUCCESS) {
      commonLog('getting client settings failed, statusCode = ${response.statusCode}');
      return FacialVerificationInitializeErrorResult(apiErrorResponse: response);
    }

    commonLog('initializing SDK');
    final EkycBridgeResult<void> result = await ekycBridge.initEkyc(
      jsonConfigurationByServer: json.encode(response.settings),
      languageCode: args.languageCode,
    );

    if (result.isSuccess) {
      commonLog('SDK initialized successfully');
      isInitialized = true;
      return FacialVerificationInitializeSuccessResult();
    } else {
      commonLog('SDK initialization failed, errorReason = ${result.errorReason}');
      return FacialVerificationInitializeErrorResult(bridgeErrorReason: result.errorReason);
    }
  }

  // Method to start capturing facial verification
  // remember to call initialize before calling this method,
  // otherwise it will return error FacialVerificationStartCapturingErrorNotInitialized
  Future<FacialVerificationStartCapturingResult> startCapturing({
    required FacialVerificationStartCapturingArgs args,
  }) async {
    if (isInitialized == false) {
      commonLog('SDK not initialized');
      return FacialVerificationStartCapturingErrorNotInitialized(
        livenessMode: args.livenessMode,
      );
    }

    commonLog('start capturing with livenessMode = ${args.livenessMode}');
    final EkycBridgeResult<EkycBridgeSelfieCapturingResult> result =
        await ekycBridge.startSelfieCapturing(
      livenessMode: args.livenessMode,
      onNewFrameBatchListener: handleOnNewFrameBatch,
    );

    if (result.isSuccess == false) {
      commonLog('start capturing failed, errorReason = ${result.errorReason}');
      return FacialVerificationStartCapturingErrorResult(
        bridgeErrorReason: result.errorReason,
        livenessMode: args.livenessMode,
      );
    }

    return handleBridgeResult(result: result, livenessMode: args.livenessMode);
  }

  @visibleForTesting
  Future<FacialVerificationStartCapturingResult> handleBridgeResult({
    required EkycBridgeResult<EkycBridgeSelfieCapturingResult> result,
    required EkycBridgeLivenessMode livenessMode,
  }) async {
    commonLog(
      'selfie capturing success, uploading images to server and waiting for batch frame to be uploaded',
    );
    final List<dynamic> parallelResults = await Future.wait(
      <Future<dynamic>>[
        uploadImages(
          images: result.data?.images ?? <EkycBridgeImage>[],
        ),
        Future.wait(
          uploadFrameBatchCompleterList.map((Completer<void> completer) => completer.future),
        ),
      ],
    );

    // remove invalid frameBatch - refer: https://ekyc.trustingsocial.com/sdks/React-Native-SDK#3341-remove-invalid-frame-batch-ids
    final List<String> uploadedFileIds = getValidUploadedFileIds(
      result.data?.validFrameBatchIds ?? <String>[],
    );

    // clear capturing temporary data, to avoid memory leak and data inconsistency
    clearFrameBatchTemporaryData();

    final EkycUploadImagesEntity uploadImagesEntity = parallelResults[0];
    if (uploadImagesEntity.statusCode != CommonHttpClient.SUCCESS) {
      commonLog('uploading images failed, statusCode = ${uploadImagesEntity.statusCode}');
      return FacialVerificationStartCapturingErrorResult(
        apiErrorResponse: uploadImagesEntity,
        livenessMode: livenessMode,
      );
    }

    commonLog(
      'returning data to host-app, imageIds = ${uploadImagesEntity.imageIds}, videoIds = $uploadedFileIds',
    );
    return FacialVerificationStartCapturingSuccessResult(
      imageIds: uploadImagesEntity.imageIds,
      videoIds: uploadedFileIds,
      livenessMode: livenessMode,
    );
  }

  @visibleForTesting
  List<String> getValidUploadedFileIds(List<String> validFrameBatchIds) {
    return validFrameBatchIds
        .map((String validFrameBatchId) => frameBatchIdUploadedFileIdMap[validFrameBatchId])
        .nonNulls
        .toList();
  }

  @visibleForTesting
  Future<EkycUploadImagesEntity> uploadImages({
    required List<EkycBridgeImage> images,
  }) async {
    final EkycUploadImagesEntity response = await ekycRepo.uploadImages(
      request: EkycUploadImagesRequest(
        images: images,
        sessionToken: sessionToken,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockUploadImagesCaseFileName(MockUploadImagesCase.success),
      ),
    );
    return response;
  }

  @visibleForTesting
  Future<void> handleOnNewFrameBatch(EkycBridgeFrameBatch frameBatch) async {
    commonLog('receive new frame batch id = ${frameBatch.id} from SDK');
    final String? frameBatchId = frameBatch.id;
    if (frameBatchId == null) {
      return;
    }

    final Completer<void> completer = Completer<void>();
    commonLog('uploading frame batch id = $frameBatchId to server');
    // add completer to the list to make sure that all frames are uploaded
    uploadFrameBatchCompleterList.add(completer);
    final EkycUploadFramesEntity uploadFramesEntity = await ekycRepo.uploadFrames(
      request: EkycUploadFramesRequest(
        frameBatch: frameBatch,
        sessionToken: sessionToken,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockUploadFramesCaseFileName(MockUploadFramesCase.success),
      ),
    );

    completer.complete();
    if (uploadFramesEntity.statusCode != CommonHttpClient.SUCCESS) {
      // will ignore this error
      // refer dive-in diagram for more details https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3854041138/EMA-5176+Mobile+Dive-in+Face+Authentication#Diagram
      commonLog(
        'uploading frame batch id = $frameBatchId failed, statusCode = ${uploadFramesEntity.statusCode}',
      );
      return;
    }
    final String? uploadedFileId = uploadFramesEntity.fileIds?.firstOrNull;
    if (uploadedFileId != null) {
      commonLog(
        'frame batch id = $frameBatchId uploaded successfully, uploadedFileId = $uploadedFileId',
      );
      frameBatchIdUploadedFileIdMap[frameBatchId] = uploadedFileId;
    }
  }

  @visibleForTesting
  void clearSessionData() {
    commonLog('clear old session data');
    clearFrameBatchTemporaryData();
    sessionToken = null;
    isInitialized = false;
  }

  @visibleForTesting
  void clearFrameBatchTemporaryData() {
    commonLog('clear frame batch temporary data');
    uploadFrameBatchCompleterList.clear();
    frameBatchIdUploadedFileIdMap = <String, String>{};
  }
}
