// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../../../../data/response/base_entity.dart';
import '../../bridges/models/ekyc_bridge_error_reason.dart';
import '../../bridges/models/ekyc_bridge_liveness_mode.dart';

abstract class FacialVerificationStartCapturingResult {
  final bool isSuccess;
  final EkycBridgeLivenessMode livenessMode;

  FacialVerificationStartCapturingResult({
    required this.isSuccess,
    required this.livenessMode,
  });
}

class FacialVerificationStartCapturingErrorNotInitialized
    extends FacialVerificationStartCapturingResult {
  FacialVerificationStartCapturingErrorNotInitialized({
    required super.livenessMode,
  }) : super(isSuccess: false);
}

class FacialVerificationStartCapturingSuccessResult extends FacialVerificationStartCapturingResult {
  final List<String>? videoIds;
  final List<String>? imageIds;

  FacialVerificationStartCapturingSuccessResult({
    required super.livenessMode,
    this.videoIds,
    this.imageIds,
  }) : super(isSuccess: true);
}

class FacialVerificationStartCapturingErrorResult extends FacialVerificationStartCapturingResult {
  final BaseEntity? apiErrorResponse;
  final EkycBridgeErrorReason? bridgeErrorReason;

  FacialVerificationStartCapturingErrorResult({
    required super.livenessMode,
    this.apiErrorResponse,
    this.bridgeErrorReason,
  }) : super(isSuccess: false);
}
