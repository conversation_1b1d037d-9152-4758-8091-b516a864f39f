// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../global_key_provider.dart';

extension CommonStringTranslateExtension on String {
  String translate({
    List<String>? args,
    Map<String, String>? namedArgs,
    String? gender,
  }) =>
      this.tr(
        args: args,
        namedArgs: namedArgs,
        gender: gender,
        context: navigatorContext,
      );
}

extension LocalizationBuildContextExt on BuildContext {
  List<LocalizationsDelegate<dynamic>>? get localizationDelegates =>
      EasyLocalization.of(this)?.delegates;

  List<Locale>? get supportedLocales => EasyLocalization.of(this)?.supportedLocales;

  Locale? get locale => EasyLocalization.of(this)?.locale;

  Future<void> setLocale(Locale val) async => EasyLocalization.of(this)?.setLocale(val);
}
