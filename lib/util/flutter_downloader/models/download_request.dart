// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

class DownloadRequest {
  final String url;
  final Map<String, String>? headers;
  final String? saveDir;
  final bool showNotification;
  final bool openFileFromNotification;
  final String? suggestedFilename;
  final bool hasOpenFile;

  DownloadRequest({
    required this.url,
    this.headers,
    this.saveDir,
    this.showNotification = false,
    this.openFileFromNotification = false,
    this.suggestedFilename,
    this.hasOpenFile = false,
  });
}
