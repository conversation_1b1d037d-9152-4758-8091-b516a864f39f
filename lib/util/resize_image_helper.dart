// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../ui_model/size_image_model.dart';
import 'functions.dart';

class ResizeImageHelper {
  static final ResizeImageHelper _singleton = ResizeImageHelper._internal();

  factory ResizeImageHelper() {
    return _singleton;
  }

  ResizeImageHelper._internal();

  SizeImage? cacheImageSize(BuildContext context,
      {required BoxConstraints constraints,
      int? memCacheWidth,
      int? memCacheHeight,
      double? width,
      double? height,
      double? pixelRatio}) {
    if (memCacheWidth != null) {
      return SizeImage(memCacheWidth, null);
    }

    if (memCacheHeight != null) {
      return SizeImage(null, memCacheHeight);
    }

    if (width != null) {
      return SizeImage(
          convertValuePhysicalPixelsImage(context, width, pixelRatio: pixelRatio), null);
    }

    if (height != null) {
      return SizeImage(
          null, convertValuePhysicalPixelsImage(context, height, pixelRatio: pixelRatio));
    }

    final SizeImage imageConstraint =
        cacheImageSizeConstraint(context, constraints: constraints, pixelRatio: pixelRatio);
    return imageConstraint;
  }

  SizeImage cacheImageSizeConstraint(BuildContext context,
      {required BoxConstraints constraints, double? pixelRatio}) {
    if (constraints.maxWidth != double.infinity && constraints.maxHeight != double.infinity) {
      if (constraints.maxWidth >= constraints.maxHeight) {
        return SizeImage(
            null,
            convertValuePhysicalPixelsImage(context, constraints.maxHeight,
                pixelRatio: pixelRatio));
      } else {
        return SizeImage(
            convertValuePhysicalPixelsImage(context, constraints.maxWidth, pixelRatio: pixelRatio),
            null);
      }
    }

    if (constraints.maxWidth != double.infinity) {
      return SizeImage(
          convertValuePhysicalPixelsImage(context, constraints.maxWidth, pixelRatio: pixelRatio),
          null);
    } else if (constraints.maxHeight != double.infinity) {
      return SizeImage(null,
          convertValuePhysicalPixelsImage(context, constraints.maxHeight, pixelRatio: pixelRatio));
    }

    return const SizeImage(null, null);
  }

  /*
  Docs: https://api.flutter.dev/flutter/dart-ui/FlutterView/devicePixelRatio.html
  The number of device pixels for each logical pixel for the screen this view is displayed on.
  This number might not be a power of two. Indeed, it might not even be an integer. For example, the Nexus 6 has a device pixel ratio of 3.5.
  Device pixels are also referred to as physical pixels. Logical pixels are also referred to as device-independent or resolution-independent pixels.
   */
  int? convertValuePhysicalPixelsImage(BuildContext context, double? value, {double? pixelRatio}) {
    try {
      if (value == null || value < 0 || value == double.infinity) return null;

      return (value * (pixelRatio ?? commonUtilFunction.devicePixelRatio(context))).ceil();
    } on Exception catch (_) {
      return null;
    }
  }
}
