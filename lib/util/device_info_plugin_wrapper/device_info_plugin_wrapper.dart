// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/cupertino.dart';

import '../device_platform.dart';
import '../../common_package/common_package.dart';

class DeviceInfoPluginWrapper {
  @visibleForTesting
  AndroidDeviceInfo? androidInfo;
  @visibleForTesting
  IosDeviceInfo? iosInfo;

  final DeviceInfoPlugin deviceInfo;
  final DevicePlatform platform;

  DeviceInfoPluginWrapper({
    required this.deviceInfo,
    required this.platform,
  });

  Future<void> initDeviceInfo() async {
    if (iosInfo != null || androidInfo != null) {
      return;
    }

    if (platform.isAndroid()) {
      androidInfo = await deviceInfo.androidInfo;
    } else if (platform.isIOS()) {
      iosInfo = await deviceInfo.iosInfo;
    } else {
      throw Exception('Unsupported platform');
    }
  }

  String getPlatformName() {
    if (platform.isAndroid()) {
      return 'android';
    }

    if (platform.isIOS()) {
      return 'ios';
    }

    throw Exception('Unsupported platform');
  }

  String? getDeviceModel() {
    if (platform.isAndroid()) {
      final AndroidDeviceInfo? info = androidInfo;
      if (info == null) {
        return null;
      }

      return '${info.manufacturer} ${info.model}';
    }

    if (platform.isIOS()) {
      return iosInfo?.utsname.machine;
    }

    throw Exception('Unsupported platform');
  }

  String? getOSVersion() {
    if (platform.isAndroid()) {
      return androidInfo?.version.sdkInt.toString();
    }

    if (platform.isIOS()) {
      return iosInfo?.systemVersion;
    }

    throw Exception('Unsupported platform');
  }

  /// In Android OS, it's called build ID
  /// Refer to: https://source.android.com/docs/setup/about/build-numbers#source-code-tags-and-builds
  /// Refer for the format of the build ID: https://source.android.com/docs/setup/about/build-numbers#build-ids-defined
  /// I was only able to obtain the OS build number for the Android platform because
  /// I could not find an official document or library support to obtain it for iOS.
  String? getAndroidBuildNumber() {
    return androidInfo?.id;
  }
}
