// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/cupertino.dart';

import '../base/page_base.dart';

abstract class CommonNavigator {
  /// Navigate to given page base  w/ optional parameters, e.g.
  /// `name='person', params={'fid': 'f2', 'pid': 'p1'}`
  void goPage(BuildContext context, PageBase pageBase, {PageBaseArg? extra});

  /// Navigate to a named route w/ optional parameters, e.g.
  /// `name='person', params={'fid': 'f2', 'pid': 'p1'}`
  void goNamed(BuildContext context, String pageName, {PageBaseArg? extra});

  /// Push a given page base  onto the page stack w/ optional parameters, e.g.
  /// `name='person', params={'fid': 'f2', 'pid': 'p1'}`
  void pushPage(BuildContext context, PageBase pageBase, {PageBaseArg? extra});

  /// Push a route named  onto the page stack w/ optional parameters, e.g.
  /// `name='person', params={'fid': 'f2', 'pid': 'p1'}`
  void pushNamed(BuildContext context, String pageName, {PageBaseArg? extra});

  /// Replaces the top-most page of the page stack with pageBase w/
  /// optional parameters, e.g. `name='person', params={'fid': 'f2', 'pid':
  /// 'p1'}`.
  void pushReplacementPage(BuildContext context, PageBase pageBase, {PageBaseArg? extra});

  /// Replaces the top-most page of the page stack with the named route w/
  /// optional parameters, e.g. `name='person', params={'fid': 'f2', 'pid':
  /// 'p1'}`.
  void pushReplacementNamed(BuildContext context, String pageName, {PageBaseArg? extra});

  /// Pop the top-most route off the current screen.
  /// If the top-most route is a pop up or dialog,
  void pop(BuildContext context, {Object? result});

  /// Returns `true` if there is at least two or more route can be pop.
  bool canPop(BuildContext context);

  /// Consults the current route's method, and acts accordingly,
  /// potentially popping the route as a result; returns whether the pop request
  /// should be considered handled.
  bool maybePop(BuildContext context, {Object? result});

  /// Pop until [pageName] or pop until 1st route if [pageName] doesn't exist in stack.
  /// [onPageFound] will be called if [pageName] exist in stack.
  /// [onPageNotFound] will be call if [pageName] doesn't exist in stack.
  void popUntilNamed(BuildContext context, String pageName,
      {Function? onPageFound, Function? onPageNotFound});

  /// Pop until [pageName] or pop until 1st route if [pageName] doesn't exist in stack.
  /// If [pageName] doesn't exist in stack, the function will replace the last route
  /// by [pageName].
  /// [onDone] will be called after popping or replacing done.
  void popUntilAndReplaceNamed(BuildContext context, String pageName,
      {PageBaseArg? extra, Function? onDone});

  /// Pop until reach predicate and replace by [pageName].
  void removeUntilAndPushReplacementNamed(
      BuildContext context, String pageName, RoutePredicate predicate,
      {PageBaseArg? extra});
}
