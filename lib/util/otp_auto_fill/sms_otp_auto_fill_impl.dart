// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/services.dart';
import 'package:otp_autofill/otp_autofill.dart';

import 'otp_auto_fill.dart';

class SmsOtpAutoFillImpl extends OtpAutoFill {
  /// this is define from native side (android).
  static const String timeoutExceptionCode = '408';

  final OTPInteractor otpInteractor;

  SmsOtpAutoFillImpl(this.otpInteractor);

  @override
  Future<void> startUserConsentListening(
      {required String Function(String?) onExtractCode,
      required void Function(String code) onCode,
      void Function(OtpAutoFillException e)? onError,
      String? senderNumber,
      bool autoStop = true}) async {
    try {
      final String? smsContent = await otpInteractor.startListenUserConsent(senderNumber);

      if (autoStop) {
        stopListening();
      }

      final String code = onExtractCode(smsContent);
      onCode.call(code);
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      if (autoStop) {
        stopListening();
      }

      if (e is PlatformException && e.code == timeoutExceptionCode) {
        onError?.call(OtpAutoFillException(
          reason: OtpAutoFillExceptionReason.timeout,
          message: 'timeout',
        ));
      } else if (e is PlatformException) {
        onError?.call(OtpAutoFillException(
          reason: OtpAutoFillExceptionReason.nativePlatform,
          message: e.message ?? OtpAutoFillException.unknownMessage,
        ));
      } else if (e is UnsupportedPlatform) {
        onError?.call(OtpAutoFillException(
          reason: OtpAutoFillExceptionReason.unsupported,
          message: e.toString(),
        ));
      } else {
        onError?.call(OtpAutoFillException(
          reason: OtpAutoFillExceptionReason.others,
          message: e.toString(),
        ));
      }
    }
  }

  @override
  Future<void> stopListening() {
    return otpInteractor.stopListenForCode();
  }
}
