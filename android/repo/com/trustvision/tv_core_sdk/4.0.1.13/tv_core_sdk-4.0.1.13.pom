<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.trustvision</groupId>
  <artifactId>tv_core_sdk</artifactId>
  <version>4.0.1.13</version>
  <packaging>aar</packaging>
  <dependencies>
    <dependency>
      <groupId>com.github.tsocial</groupId>
      <artifactId>CameraView</artifactId>
      <version>2.7.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>viewbinding</artifactId>
      <version>7.2.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.trustvision</groupId>
      <artifactId>tv_api_sdk</artifactId>
      <version>4.0.1.13</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.trustvision</groupId>
      <artifactId>tv_open_cv</artifactId>
      <version>4.0.1.13</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>core</artifactId>
      <version>3.3.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.appcompat</groupId>
      <artifactId>appcompat</artifactId>
      <version>1.5.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.constraintlayout</groupId>
      <artifactId>constraintlayout</artifactId>
      <version>2.0.4</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.recyclerview</groupId>
      <artifactId>recyclerview</artifactId>
      <version>1.2.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.android.material</groupId>
      <artifactId>material</artifactId>
      <version>1.9.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.tensorflow</groupId>
      <artifactId>tensorflow-lite</artifactId>
      <version>2.16.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.9</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.support</groupId>
      <artifactId>multidex</artifactId>
      <version>1.0.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jmrtd</groupId>
      <artifactId>jmrtd</artifactId>
      <version>0.7.19</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>net.sf.scuba</groupId>
      <artifactId>scuba-sc-android</artifactId>
      <version>0.0.18</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.fragment</groupId>
      <artifactId>fragment-ktx</artifactId>
      <version>1.5.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.madgag.spongycastle</groupId>
      <artifactId>prov</artifactId>
      <version>1.54.0.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.github.Tgo1014</groupId>
      <artifactId>JP2ForAndroid</artifactId>
      <version>1.0.4</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.github.mhshams</groupId>
      <artifactId>jnbis</artifactId>
      <version>1.1.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <version>1.65</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.rmtheis</groupId>
      <artifactId>tess-two</artifactId>
      <version>9.1.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.airbnb.android</groupId>
      <artifactId>lottie</artifactId>
      <version>5.2.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.github.lzyzsd</groupId>
      <artifactId>circleprogress</artifactId>
      <version>1.2.1</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
