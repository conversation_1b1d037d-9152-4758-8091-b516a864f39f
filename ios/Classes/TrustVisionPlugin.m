#import "TrustVisionPlugin.h"
#if __has_include(<trust_vision_plugin/trust_vision_plugin-Swift.h>)
#import <trust_vision_plugin/trust_vision_plugin-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "trust_vision_plugin-Swift.h"
#endif

@implementation TrustVisionPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftTrustVisionPlugin registerWithRegistrar:registrar];
}
@end
