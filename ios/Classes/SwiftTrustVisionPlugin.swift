import Flutter
import TrustVisionAPI
import TrustVisionCoreSDK
import TrustVisionSDK
import UIKit

enum SupportMethod: String {
    case getPlatformVersion
    
    // SDK METHOD NAMES
    case initialize
    case startSelfieCapturing
    case startIdCapturing
    case startFlow
    case faceMatching
    case getCardTypes
    case getSelfieCameraMode
    case getLivenessOption
    case checkNfcSupport
    case readNfc
    case scanQrCode
    case closeScreenOfSDK
}

struct Constants {
    static let METHOD_CHANNEL_NAME = "tv_plugin"
    
    static let METHOD_NAME_ON_NEW_FRAME_BATCH = "onNewFrameBatch";
    
    // INIT PARAMETERS
    static let TV_ACCESS_KEY_ID_PARAM = "tv_access_key_id_param"
    static let TV_ACCESS_KEY_SECRET_PARAM = "tv_access_key_secret_param"
    static let TV_ENDPOINT_PARAM = "tv_endpoint_param"
    static let TV_X_REQUEST_ID_PARAM = "tv_x_request_id_param"
    static let TV_X_LENDER_REQUEST_ID_PARAM = "tv_x_lender_request_id_param"
    static let TV_IS_FORCED = "tv_is_forced"
    static let TV_JSON_CONFIG_PARAM = "tv_json_config_param"
    static let TV_LANG_CODE_PARAM = "tv_lang_code_param"
    
    
    
    // TRANSACTION PARAMETER
    static let TV_REFERENCE_ID_PARAM = "tv_reference_id_param"
    
    // CAPTURING PARAMETERS
    static let TV_CONFIGURATION_PARAM = "tv_configuration_param"
    
    // FACE MATCHING PARAMETERS
    static let TV_FACE_MATCHING_IMAGE1_PARAM = "tv_face_matching_image1_id_param"
    static let TV_FACE_MATCHING_IMAGE2_PARAM = "tv_face_matching_image2_id_param"
    
}

struct PluginError {
    static let INTERNAL_ERROR = "internal_error"
    static let PARAM_MISSING_MESSAGE = "Param missing"
    static let SDK_CANCELED = "sdk_canceled"
    static let SDK_CANCELED_MESSAGE = "sdk is canceled by user"
}

public class SwiftTrustVisionPlugin: NSObject, FlutterPlugin {
    
    static var channel : FlutterMethodChannel?
    
    public override init() {
        super.init()
    }
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        channel = FlutterMethodChannel(name: Constants.METHOD_CHANNEL_NAME, binaryMessenger: registrar.messenger())
        let instance = SwiftTrustVisionPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel!)
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        
        DispatchQueue.main.async(execute: { [self] in
            switch SupportMethod(rawValue: call.method) {
            case .initialize:
                if let args = call.arguments as? NSDictionary {
                    self.initializeSdk(
                        accessKeyId: args[Constants.TV_ACCESS_KEY_ID_PARAM] as? String,
                        accessKeySecret: args[Constants.TV_ACCESS_KEY_SECRET_PARAM] as? String,
                        endpoint: args[Constants.TV_ENDPOINT_PARAM] as? String,
                        xRequestId: args[Constants.TV_X_REQUEST_ID_PARAM] as? String,
                        xLenderRequestId: args[Constants.TV_X_LENDER_REQUEST_ID_PARAM] as? String,
                        isForced: args[Constants.TV_IS_FORCED] as? Bool ?? true,
                        jsonConfigurationByServer: args[Constants.TV_JSON_CONFIG_PARAM] as? String,
                        languageCode:  args[Constants.TV_LANG_CODE_PARAM] as? String,
                        result: result)
                } else {
                    result(FlutterError.init(code: PluginError.INTERNAL_ERROR, message: PluginError.PARAM_MISSING_MESSAGE, details: "TrustVisionSDK got error"))
                }
            case .getPlatformVersion:
                result("iOS " + UIDevice.current.systemVersion)
                break
                
            case .faceMatching:
                if let args = call.arguments as? NSDictionary {
                    self.faceMatching(
                        image1Id: args[Constants.TV_FACE_MATCHING_IMAGE1_PARAM] as? String,
                        image2Id: args[Constants.TV_FACE_MATCHING_IMAGE2_PARAM] as? String,
                        result: result
                    )
                } else {
                    result(FlutterError.init(code: PluginError.INTERNAL_ERROR, message: PluginError.PARAM_MISSING_MESSAGE, details: "TrustVisionSDK got error"))
                }
                
            case .getCardTypes:
                self.getCardTypes(result: result)
                
            case .getLivenessOption:
                self.getLivenessOptions(result: result)
                
            case .getSelfieCameraMode:
                self.getSelfieCameraMode(result: result)
                
            case .startSelfieCapturing:
                if let args = call.arguments as? NSDictionary, let configDict = args[Constants.TV_CONFIGURATION_PARAM] as? NSDictionary {
                    self.startSelfieCapturing(configDict: configDict, result: result) { frameBacth in
                        SwiftTrustVisionPlugin.channel?.invokeMethod(Constants.METHOD_NAME_ON_NEW_FRAME_BATCH, arguments: frameBacth )
                        
                    }
                    
                } else {
                    result(FlutterError.init(code: PluginError.INTERNAL_ERROR, message: PluginError.PARAM_MISSING_MESSAGE, details: "TrustVisionSDK got error"))
                }
                
            case .startIdCapturing:
                if let args = call.arguments as? NSDictionary, let configDict = args[Constants.TV_CONFIGURATION_PARAM] as? NSDictionary {
                    self.startIdCapturing(configDict: configDict, result: result)
                } else {
                    self.returnParamInvalid(result: result)
                }
                
                
            case .scanQrCode:
                if let args = call.arguments as? NSDictionary, let configDict = args[Constants.TV_CONFIGURATION_PARAM] as? NSDictionary {
                    self.startScanQrCode(configDict: configDict, result: result)
                } else {
                    self.returnParamInvalid(result: result)
                    
                }
                
            case .startFlow:
                if let args = call.arguments as? NSDictionary, let configDict = args[Constants.TV_CONFIGURATION_PARAM] as? NSDictionary {
                    self.startFlow(configDict: configDict, result: result)
                } else {
                    result(FlutterError.init(code: PluginError.INTERNAL_ERROR, message: PluginError.PARAM_MISSING_MESSAGE, details: "TrustVisionSDK got error"))
                }
                
            case .closeScreenOfSDK:
                self.closeScreenOfSDK(result: result)
                
            default:
                print("Trust Vision Flutter plugin called an unsupported method: \(call.method)")
                result(FlutterMethodNotImplemented)
            }
        })
    }
    
}


extension FlutterError: Error {}

extension SwiftTrustVisionPlugin {
    
    private func pluginRootViewController() -> UIViewController { return UIApplication.shared.delegate?.window??.rootViewController ?? { fatalError() }() }
    
    private func present(vc: UIViewController?) {
        if let vc = vc {
            vc.modalPresentationStyle = .fullScreen
            self.pluginRootViewController().present(vc, animated: true, completion: nil)
        }
    }

    func closeScreenOfSDK(result: @escaping FlutterResult) {
        self.pluginRootViewController().dismiss(animated: true, completion: nil)
        result("closeScreenOfSDK")
    }
    
    
    // MARK: - Get settings
    func getCardTypes(result: @escaping FlutterResult) {
        let cardTypes = TrustVisionSdk.shared.getCardTypes()
        var cardTypeDicts = [NSDictionary]()
        for cardType in cardTypes {
            let cardTypeDict = toDictionary(cardType)
            cardTypeDicts.append(cardTypeDict)
        }
        result(cardTypeDicts)
    }
    
    func getSelfieCameraMode(result: @escaping FlutterResult) {
        let cameraMode = TrustVisionSdk.shared.getSelfieCameraMode()
        result(cameraMode)
    }
    
    func getLivenessOptions(result: @escaping FlutterResult) {
        let livenessOptions = TrustVisionSdk.shared.getLivenessOptions()
        result(livenessOptions)
    }
    
    func getIdCardSanityCheckingEnable(result: @escaping FlutterResult) {
        let enableIdCardSanityChecking = TrustVisionSdk.shared.getIdCardSanityCheckingEnable()
        result(enableIdCardSanityChecking)
    }
    
    func getSelfieSanityCheckingEnable(result: @escaping FlutterResult) {
        let enableSelfieSanityChecking = TrustVisionSdk.shared.getSelfieSanityCheckingEnable()
        result(enableSelfieSanityChecking)
    }
    
    func initializeSdk(accessKeyId: String?,
                       accessKeySecret: String?,
                       endpoint: String?,
                       xRequestId: String?,
                       xLenderRequestId: String?,
                       isForced: Bool?,
                       jsonConfigurationByServer: String?,
                       languageCode: String? ,
                       result: @escaping FlutterResult) {
        
        let sdkMode = "initialize"
        
        
        let configuration = TVInitializeConfigurationBuilder()
            .setAccessKeyId(accessKeyId)
            .setAccessKeySecret(accessKeySecret)
            .setBaseUrl(endpoint)
            .setClientSettingsJsonString(jsonConfigurationByServer)
            .setLanguageCode(languageCode)
            .setXRequestId(xRequestId)
            .setXRequestId2(xLenderRequestId)
            .setIsForced(isForced ?? true)
            .build()
        
        TrustVisionSdk.shared.initialize(
            config: configuration,
            success: {
                result("initSuccess")
            }, failure: { tvError in
                self.onDetectionError(sdkMode: sdkMode, tvError: tvError, result: result)
            }, onEvent: { tvTrackingEvent in
                // Do nothing
            }
        )
    }
    
    func startSelfieCapturing(configDict: NSDictionary,
                              result: @escaping FlutterResult,
                              onNewFrameBatch: @escaping ([String: Any?])-> Void) {
        
        print("Selfied config: \(configDict as AnyObject)")
        let configs = TVSelfieConfiguration.dictToObj(dict: configDict)
        let sdkMode = "LIVENESS".lowercased()
        
        if (configs == nil) {
            returnParamInvalid(result: result)
            return
        }
        
        do {
            let cameraViewController =  try TrustVisionSdk.shared.startSelfieCapturing(
                configuration: configs!,
                framesRecordedCallback: {[weak self](batchId, frames, metadata, currentBatchIds, _) in
                    guard let strongSelf = self else { return; }
                    
                    var frameBacthDist: [String: Any?] = [
                        "id": batchId,
                        "frames": frames["frames"],
                        "metadata": metadata,
                        "validVideoIds" : currentBatchIds
                    ]
                    
                    onNewFrameBatch(frameBacthDist)
                    
                },
                success: { capturingResult in
                    self.onDetectionSuccess(sdkMode: sdkMode, detectionResult: capturingResult, result: result)
                },
                failure: { error in
                    self.onDetectionError(sdkMode: sdkMode, tvError: error, result: result)
                },
                cancellation: {
                    self.onCancel(sdkMode: sdkMode, result: result)
                }
            )
            
            present(vc: cameraViewController)
        } catch  {
            print("startSelfieCapturing Unexpected error: \(error).")
            self.onCancel(sdkMode: sdkMode, result: result)
        }
    }
    
    private func startIdCapturing(configDict: NSDictionary, result: @escaping FlutterResult) {
        print("ID config: \(configDict as AnyObject)")
        
        let configs = TVIdCardConfiguration.dictToObj(dict: configDict as NSDictionary)
        
        let sdkMode = "read_card_info_one_side".lowercased()
        
        if (configs == nil) {
            returnParamInvalid(result: result)
            return
        }
        do {
            let cameraViewController = try TrustVisionSdk.shared.startIdCapturing(
                configuration: configs!,
                framesRecordedCallback: {[weak self](batchId, frames, metadata, currentBatchIds, _) in
                    guard let strongSelf = self else { return; }
                    // Do nothing
                    
                    print("Frame====== \(frames)")
                    
                },
                success: { capturingResult in
                    self.onDetectionSuccess(sdkMode: sdkMode, detectionResult: capturingResult, result: result)
                },
                failure: { error in
                    self.onDetectionError(sdkMode: sdkMode, tvError: error, result: result)
                },
                cancellation: {
                    self.onCancel(sdkMode: sdkMode, result: result)
                }
            )
            
            present(vc: cameraViewController)
        } catch {
            print("startIdCapturing Unexpected error: \(error).")
            self.onCancel(sdkMode: sdkMode, result: result)
        }
    }
    
    private func startScanQrCode (configDict: NSDictionary, result: @escaping FlutterResult) {
        
        let config = TVQRConfiguration.dictToObj(dict: configDict as NSDictionary)
        
        let sdkMode = "scan_qrcode".lowercased()
        
        do {
            if (config == nil) {
                returnParamInvalid(result: result)
                return
            }
            
            let vc = try TrustVisionSdk.shared.startQRScanning(configuration: config!, success: {  [weak self] (detectionResult) in
                
                self?.onDetectionSuccess(sdkMode: sdkMode, detectionResult: detectionResult, result: result)
                
            }, failure: { (error) in
                
                
                
                self.onDetectionError(sdkMode: sdkMode, tvError: error, result: result)
            }, cancellation: {
                self.onCancel(sdkMode: sdkMode, result: result)
            })
            
            vc.modalPresentationStyle = .overCurrentContext
            present(vc: vc)
            
        } catch {
            self.onCancel(sdkMode: sdkMode, result: result)
        }
    }
    
    //    private func checkNfcSupport(result: @escaping FlutterResult) {
    //        result(["isNfcSupported": TrustVisionSdk.shared.isNfcSupport()])
    //    }
    
    //    private func readNfc(configDict: NSDictionary, result: @escaping FlutterResult) {
    //        let configs = TVIdNfcConfiguration.dictToObj(dict: configDict as NSDictionary)
    //        let sdkMode = "read_nfc".lowercased()
    //
    //        if (configs == nil) {
    //            returnParamInvalid(result: result)
    //            return
    //        }
    //
    //        do {
    //            let nfcViewController = try TrustVisionSdk.shared.startNfcScanning(
    //                configuration: configs!,
    //                success: { [weak self] (detectionResult) in
    //                    // guard let strongSelf = self else { return }
    //                    print("[VKN] RESULT. \(detectionResult)")
    //
    //                    self?.onDetectionSuccess(sdkMode: sdkMode, detectionResult: detectionResult, result: result)
    //                },
    //                failure: { (error) in
    //                    self.onDetectionError(sdkMode: sdkMode, tvError: error, result: result)
    //                }) {
    //                    self.onCancel(sdkMode: sdkMode, result: result)
    //                }
    //            nfcViewController.modalPresentationStyle = .overCurrentContext
    //            present(vc: nfcViewController)
    //        } catch {
    //            self.onCancel(sdkMode: sdkMode, result: result)
    //        }
    //    }
    
    private func startFlow(configDict: NSDictionary, result: @escaping FlutterResult) {
        let configs = TVSDKConfig.dictToObj(dict: configDict)
        let sdkMode = "start_flow"
        
        if (configs == nil) {
            returnParamInvalid(result: result)
            return
        }
        
        do {
            let cameraViewController = try TrustVisionSdk.shared.startFullFlow(
                config: configs!,
                success: { capturingResult in
                    self.onDetectionSuccess(sdkMode: sdkMode, detectionResult: capturingResult, result: result)
                },
                failure: { [self] error in
                    self.onDetectionError(sdkMode: sdkMode, tvError: error, result: result)
                },
                cancellation: { [self] in
                    self.onCancel(sdkMode: sdkMode, result: result)
                }
            )
            
            present(vc: cameraViewController)
        } catch {
            self.onCancel(sdkMode: sdkMode, result: result)
        }
    }
    
    private func onDetectionSuccess(sdkMode: String, detectionResult: TVDetectionResult, result: @escaping FlutterResult) {
        result(detectionResult.toDictionary())
        self.pushResultServerLog(withErrorCode: nil, sdkMode: sdkMode)
    }
    
    private func faceMatching(image1Id: String?, image2Id: String?,result: @escaping FlutterResult) {
        let sdkMode = "face_matching"
        if (image1Id == nil || image2Id == nil) {
            self.onError(sdkMode: sdkMode, errorCode: PluginError.INTERNAL_ERROR, errorMessage: "all image ids must not be null", result: result)
            return
        }
        
        TrustVisionSdk.shared.matchFace(
            image1Id: image1Id!,
            image2Id: image2Id!,
            success: { matchingResult in
                result(matchingResult.toDictionary())
                self.pushResultServerLog(withErrorCode: nil, sdkMode: sdkMode)
            },
            failure: { error in
                self.onDetectionError(sdkMode: sdkMode, tvError: error, result: result)
            }
        )
    }
    
    private func returnParamInvalid(result: @escaping FlutterResult) {
        result(FlutterError.init(code: PluginError.INTERNAL_ERROR, message: PluginError.PARAM_MISSING_MESSAGE, details: "TrustVisionSDK got error"))
    }
    
    private func onCancel(sdkMode: String, result: @escaping FlutterResult) {
        let err = FlutterError(code: PluginError.SDK_CANCELED, message: PluginError.SDK_CANCELED_MESSAGE, details: "TrustVisionSDK got error")
        result(err)
        
        TrustVisionSdk.shared.pushServerLog(tag: "result.wrapper",
                                            event: ("cancel." + sdkMode),
                                            log: Dictionary())
#if DEBUG
        print("[TrustVisionSDK] Cancelled")
#endif
    }
    
    private func onError(sdkMode: String, errorCode: String, errorMessage: String?, result: @escaping FlutterResult) {
        let error = FlutterError(code: errorCode, message: errorMessage, details: "TrustVisionSDK got error")
        result(error)
        
        pushResultServerLog(withErrorCode: errorCode, sdkMode: sdkMode)
        
#if DEBUG
        print("🔥 [TrustVisionSDK] failed: \(error)")
#endif
    }
    
    private func onDetectionError(sdkMode: String, tvError: TVError, result: @escaping FlutterResult) {
        let err = FlutterError(code: tvError.errorCode, message: tvError.description, details: "TrustVisionSDK got error")
        result(err)
        
        pushResultServerLog(withErrorCode: tvError.errorCode, sdkMode: sdkMode)
        
#if DEBUG
        print("🔥 [TrustVisionSDK] failed: \(tvError)")
#endif
    }
    
    private func clientSettingsObjFromStrings(clientSettingsString: String?) -> TVClientSettingsResponse? {
        guard let clientSettingsString_ = clientSettingsString else { return nil }
        
        let jsonData = Data(clientSettingsString_.utf8)
        let decoder = JSONDecoder()
        let clientSettings: TVClientSettingsResponse? = try? decoder.decode(TVClientSettingsResponse.self, from: jsonData)
        return clientSettings
    }
    
    private func getServerLogsdkModeString(fromIdConfig config: TVIdCardConfiguration?) -> String? {
        let subsdkMode: String
        if (config?.isReadBothSide ?? false == true) {
            subsdkMode = "_TWO_SIDE"
        } else {
            subsdkMode = "_ONE_SIDE"
        }
        return "READ_CARD_INFO\(subsdkMode)".lowercased()
    }
    
    private func pushResultServerLog(withErrorCode errorCode: String?, sdkMode: String?) {
        pushResultServerLog(withTag:"result.rn_wrapper", errorCode: errorCode, sdkMode:sdkMode)
    }
    
    func logEvent(_ sdkMode: String?, errorCode: String?) {
        pushResultServerLog(withTag: "result.rn_js", errorCode: errorCode, sdkMode: sdkMode?.lowercased())
    }
    
    private func pushResultServerLog(withTag tag: String, errorCode: String?, sdkMode: String?) {
        if let errorCode = errorCode {
            TrustVisionSdk.shared.pushServerLog(
                tag: tag,
                event: "sdk_mode.\(sdkMode ?? "")",
                log: [
                    "error": errorCode
                ])
        } else {
            TrustVisionSdk.shared.pushServerLog(
                tag: tag,
                event: "sdk_mode.\(sdkMode ?? "")",
                log: [
                    "result_object": "ok"
                ])
        }
    }
    
    
    private func getBase64String(from image: UIImage?) -> String? {
        let imageData = image?.pngData()
        let imageBase64 = imageData?.base64EncodedString(options: .endLineWithLineFeed)
        return imageBase64
    }
    
    private func toDictionary(_ obj: NSObject) -> NSDictionary {
        var count: UInt32 = 0
        
        let dictionary = NSMutableDictionary()
        let properties = class_copyPropertyList(type(of: obj), &count)
        
        for i in 0..<Int(count) {
            
            var key: String? = nil
            if let property = properties?[i] {
                key = String(utf8String: property_getName(property))
            }
            let value = obj.value(forKey: key ?? "")
            
            if let key = key, let value = value {
                if (value is NSNumber) || (value is NSString) || (value is NSDictionary) || (value is Data) {
                    dictionary[key] = value
                } else if value is UIImage {
                    let base64 = getBase64String(from: value as? UIImage)
                    dictionary[key] = base64
                } else if value is NSObject {
                    dictionary[key] = toDictionary(value as! NSObject)
                } else {
                    print("Invalid type for \(NSStringFromClass(type(of: obj).self)) (\(key))")
                }
            }
        }
        
        return dictionary
    }
}
