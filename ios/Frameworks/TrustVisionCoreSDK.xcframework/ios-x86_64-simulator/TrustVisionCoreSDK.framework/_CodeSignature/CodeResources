<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/ALView+PureLayout.h</key>
		<data>
		PfAnIOKq4Itz6kd1EgGTBqrnAQY=
		</data>
		<key>Headers/FileDecrypter.h</key>
		<data>
		8QrRtfPtawdmvnTUu2tV29Ubfqk=
		</data>
		<key>Headers/FileEncrypter.h</key>
		<data>
		ps4jv72hErCWmoiZGdmA0PWKQVo=
		</data>
		<key>Headers/NSArray+PureLayout.h</key>
		<data>
		1xP2FIrTGjLhMDDgYL2MVpGis3s=
		</data>
		<key>Headers/NSLayoutConstraint+PureLayout.h</key>
		<data>
		i3nkBt++CJdf1VTapzvLXvF+J1Q=
		</data>
		<key>Headers/PureLayout+Internal.h</key>
		<data>
		hvxFbAnZ29P4vdCqMZVQJn5saKo=
		</data>
		<key>Headers/PureLayout.h</key>
		<data>
		9I6p/nWmol52AR130WaSnM20/7M=
		</data>
		<key>Headers/PureLayoutDefines.h</key>
		<data>
		/EMmmmBVf2gfOkjw2EG/doN7wWc=
		</data>
		<key>Headers/TrustVisionCoreSDK-Swift.h</key>
		<data>
		OEPGEuj1SJbRsXtDpqcZmjJygK4=
		</data>
		<key>Headers/TrustVisionCoreSDK-umbrella.h</key>
		<data>
		hKJaXiVhY8jLAEzgwMQv2cwb6fg=
		</data>
		<key>Headers/TrustVisionCoreSDK.h</key>
		<data>
		ZZmOlXBemSZOZFoBSIIkGATA7qo=
		</data>
		<key>Headers/aes.h</key>
		<data>
		bzQpyRf4eRsVCDc5fmQyVeapXas=
		</data>
		<key>Info.plist</key>
		<data>
		S4yqRcw7vEna1ctZabKIV4h2qBE=
		</data>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		3WFg9simhbqXlZ6NeJv5WMq+1wg=
		</data>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		2kq9HHjn5OkBpcBjm++lfr0FBec=
		</data>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		QQcBYg+/D4W/qWC4yWibHs0ZGWs=
		</data>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		78v/6s2t4ViJWqw53EkeLNJVBCs=
		</data>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		QQcBYg+/D4W/qWC4yWibHs0ZGWs=
		</data>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		46PDETkJoD1T95uCGQWpOQfZm48=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		o0KkOg9RhFaaBD2ODiUgQmtkUXI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/ALView+PureLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			PfAnIOKq4Itz6kd1EgGTBqrnAQY=
			</data>
			<key>hash2</key>
			<data>
			m4hOYTftCC58b0KGTA3cZ0VQhNrIvOD47d3NZsplNUc=
			</data>
		</dict>
		<key>Headers/FileDecrypter.h</key>
		<dict>
			<key>hash</key>
			<data>
			8QrRtfPtawdmvnTUu2tV29Ubfqk=
			</data>
			<key>hash2</key>
			<data>
			C7GhD7EQ51gMa5igBG8Qc9R4FGbXaaTibYo3nweJtI8=
			</data>
		</dict>
		<key>Headers/FileEncrypter.h</key>
		<dict>
			<key>hash</key>
			<data>
			ps4jv72hErCWmoiZGdmA0PWKQVo=
			</data>
			<key>hash2</key>
			<data>
			WFMToICFIB6E7wWHUNp/fUJFHciqUPTqKYUCdnneiqY=
			</data>
		</dict>
		<key>Headers/NSArray+PureLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			1xP2FIrTGjLhMDDgYL2MVpGis3s=
			</data>
			<key>hash2</key>
			<data>
			SI+GsvnFsYogH9WfeuWRFqttE+vS1cDbLkpB3lC9xkM=
			</data>
		</dict>
		<key>Headers/NSLayoutConstraint+PureLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			i3nkBt++CJdf1VTapzvLXvF+J1Q=
			</data>
			<key>hash2</key>
			<data>
			bDewYxhVnyGj3htAEf0CM+xV1oHwI26Uwk8ySlheUyE=
			</data>
		</dict>
		<key>Headers/PureLayout+Internal.h</key>
		<dict>
			<key>hash</key>
			<data>
			hvxFbAnZ29P4vdCqMZVQJn5saKo=
			</data>
			<key>hash2</key>
			<data>
			7znOlVKERRP6CPaI7VgOe1nkzyDjLruQFT0x9q9Qf0w=
			</data>
		</dict>
		<key>Headers/PureLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			9I6p/nWmol52AR130WaSnM20/7M=
			</data>
			<key>hash2</key>
			<data>
			eUu5ijXppw8MGiH0yjE5qf5xfXHw8ywCurYJYV/RDEA=
			</data>
		</dict>
		<key>Headers/PureLayoutDefines.h</key>
		<dict>
			<key>hash</key>
			<data>
			/EMmmmBVf2gfOkjw2EG/doN7wWc=
			</data>
			<key>hash2</key>
			<data>
			vVh2n0KVzEXTo0FHXdcsnx0kwyjUxwUbqp53Xs8cLFI=
			</data>
		</dict>
		<key>Headers/TrustVisionCoreSDK-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			OEPGEuj1SJbRsXtDpqcZmjJygK4=
			</data>
			<key>hash2</key>
			<data>
			qdemM+SmBj3pCtjNjCqZ+tOd0nG68rqoeGDVfIeW6uw=
			</data>
		</dict>
		<key>Headers/TrustVisionCoreSDK-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			hKJaXiVhY8jLAEzgwMQv2cwb6fg=
			</data>
			<key>hash2</key>
			<data>
			gY/AGPeuu8kgDcolUpELaPSn0/Qk1Nhkytzb3598gFM=
			</data>
		</dict>
		<key>Headers/TrustVisionCoreSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZZmOlXBemSZOZFoBSIIkGATA7qo=
			</data>
			<key>hash2</key>
			<data>
			XicJy8B1Qg80wso3Piz9bwQbP14SZf/IgJQFslG+Pjk=
			</data>
		</dict>
		<key>Headers/aes.h</key>
		<dict>
			<key>hash</key>
			<data>
			bzQpyRf4eRsVCDc5fmQyVeapXas=
			</data>
			<key>hash2</key>
			<data>
			Oo72hiHVXnuxZB33b9i0qDCTXsDBSoca5u6S5DvYhK0=
			</data>
		</dict>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			3WFg9simhbqXlZ6NeJv5WMq+1wg=
			</data>
			<key>hash2</key>
			<data>
			geG6h7Sj7gr6+JFHXU5QuilzTGLzlMAEPRkOZ0jFb4g=
			</data>
		</dict>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			2kq9HHjn5OkBpcBjm++lfr0FBec=
			</data>
			<key>hash2</key>
			<data>
			3dgJzYuzOkX3Bd3ElnehOL1aCQu7oCK35lCXcU4G5Os=
			</data>
		</dict>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			QQcBYg+/D4W/qWC4yWibHs0ZGWs=
			</data>
			<key>hash2</key>
			<data>
			8mqDhc7oLo8phgxuXTheoodWiXCEdUQmtPyTHxQVtbg=
			</data>
		</dict>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			78v/6s2t4ViJWqw53EkeLNJVBCs=
			</data>
			<key>hash2</key>
			<data>
			3q1bQ+6CHpdnmPdSX6dXUC2W2odoRVFzS5RtuD7F8Qk=
			</data>
		</dict>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			QQcBYg+/D4W/qWC4yWibHs0ZGWs=
			</data>
			<key>hash2</key>
			<data>
			8mqDhc7oLo8phgxuXTheoodWiXCEdUQmtPyTHxQVtbg=
			</data>
		</dict>
		<key>Modules/TrustVisionCoreSDK.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			46PDETkJoD1T95uCGQWpOQfZm48=
			</data>
			<key>hash2</key>
			<data>
			3UOMstZ6EX9OHIEnzZrZMAqG9Sa7xb9dfNqhEezEcN0=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			o0KkOg9RhFaaBD2ODiUgQmtkUXI=
			</data>
			<key>hash2</key>
			<data>
			p3u4OGDeGpU90zBPdZVuUA31k13wCINuDNoaGxmhC9U=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
