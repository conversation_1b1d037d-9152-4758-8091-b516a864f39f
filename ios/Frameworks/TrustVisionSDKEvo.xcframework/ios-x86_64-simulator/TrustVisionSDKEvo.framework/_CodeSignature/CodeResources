<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/TrustVisionSDK.h</key>
		<data>
		TEacEeK9eW2J9sg8Fno0HteLXRc=
		</data>
		<key>Headers/TrustVisionSDKEvo-Swift.h</key>
		<data>
		yhPHCqRAJcA4aUs4aCkbhC1/Ovo=
		</data>
		<key>Headers/TrustVisionSDKEvo-umbrella.h</key>
		<data>
		iIK7lekWtYGZoUJZ4rvSu93wXpU=
		</data>
		<key>Info.plist</key>
		<data>
		sv7klyVzsWoYHJrheN5Z+rcpTV0=
		</data>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		5weljD/HiQc1SU1IWjkB5k5evQg=
		</data>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		S+S2+DM4mRLN8KO+ZR/aZn6reUc=
		</data>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		UbCsk2Mwd3nx1maEvDwrrQPIh/s=
		</data>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		dv3p7bci61R0eKN4YARyu7KjW5w=
		</data>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		UbCsk2Mwd3nx1maEvDwrrQPIh/s=
		</data>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		vrFfAy3WzAh70FEt2XPaVN5jOaw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		7eeX33fHD++RFid8zfbRYv6gDW8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/TrustVisionSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			TEacEeK9eW2J9sg8Fno0HteLXRc=
			</data>
			<key>hash2</key>
			<data>
			3ZyLFM9gztCXiqKJ7U4QQuXm6hFqCy2vLLPR08mbPrw=
			</data>
		</dict>
		<key>Headers/TrustVisionSDKEvo-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			yhPHCqRAJcA4aUs4aCkbhC1/Ovo=
			</data>
			<key>hash2</key>
			<data>
			C2GJxywJ0qNcJZGPyj+tq7J2ZwMYRakKxCdPfWJE7yw=
			</data>
		</dict>
		<key>Headers/TrustVisionSDKEvo-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			iIK7lekWtYGZoUJZ4rvSu93wXpU=
			</data>
			<key>hash2</key>
			<data>
			rJExd+aMuTiCED5L4w+4QsAkkMIBr1aF7Ds+K5QlB28=
			</data>
		</dict>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			5weljD/HiQc1SU1IWjkB5k5evQg=
			</data>
			<key>hash2</key>
			<data>
			Dh09vTG4CXyu90Nz21sMIX9gNFxyZopCI/RbxjKKh6o=
			</data>
		</dict>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			S+S2+DM4mRLN8KO+ZR/aZn6reUc=
			</data>
			<key>hash2</key>
			<data>
			DSWPaggwZjAmKwen7VaSssbOSw4S1HPYk7ujkK5vua8=
			</data>
		</dict>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			UbCsk2Mwd3nx1maEvDwrrQPIh/s=
			</data>
			<key>hash2</key>
			<data>
			4lhQBjNv8DxfbylKSgFsTb1FJL+oi5bkwgKz0GuhG3A=
			</data>
		</dict>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			dv3p7bci61R0eKN4YARyu7KjW5w=
			</data>
			<key>hash2</key>
			<data>
			REmCS8J0wPRwwwrrQhAF6+Wsbgj04+0Ap/3Kgq2AZOE=
			</data>
		</dict>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			UbCsk2Mwd3nx1maEvDwrrQPIh/s=
			</data>
			<key>hash2</key>
			<data>
			4lhQBjNv8DxfbylKSgFsTb1FJL+oi5bkwgKz0GuhG3A=
			</data>
		</dict>
		<key>Modules/TrustVisionSDKEvo.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			vrFfAy3WzAh70FEt2XPaVN5jOaw=
			</data>
			<key>hash2</key>
			<data>
			nMtEjBxap62L+pcyJO7ouv3IGxriQBdQgY1vHG4Lhuw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			7eeX33fHD++RFid8zfbRYv6gDW8=
			</data>
			<key>hash2</key>
			<data>
			hJ/EytxzJOvaVvnDa559kHG4HSzM0K+JNw2VeBEDsX4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
