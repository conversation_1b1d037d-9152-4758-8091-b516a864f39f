name: flutter_common_package
description: A collection of reusable Flutter components for Trusting Social projects.
version: 5.0.0
# Adding below line to remove warning: Publishable packages can't have git dependencies
# Remove it if you wish to publish to pub.dev
publish_to: none

homepage: https://github.com/tsocial/flutter-common-package

environment:
  sdk: ">=3.5.4 <4.0.0"
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  get_it: 8.0.3
  dio: 5.4.0
  pull_to_refresh: 2.0.0
  synchronized: 3.3.0+3
  # Clone onesignal_flutter package of Trusting Social Mobile team
  onesignal_flutter:
    git:
      url: https://github.com/tsocial/ts-onesignal-flutter-sdk.git
      ref: v3.5.1+3-TS
  otp_autofill: 4.1.0
  flutter_secure_storage: 9.0.0
  flutter_inappwebview: 6.1.5
  url_launcher: 6.3.1
  fluttertoast: 8.2.10
  device_info_plus: 11.3.0 # latest version 11.4.0 has dependencies conflict with flutter_test
  package_info_plus: 8.3.0
  store_checker: 1.4.0
  flutter_svg: 2.0.16
  cached_network_image: 3.4.1
  equatable: 2.0.7
  pin_code_fields: 8.0.1
  flutter_bloc: 8.1.6
  shared_preferences: 2.3.3
  async: 2.11.0
  connectivity_plus: 6.1.1
  flutter_keyboard_visibility: 6.0.0
  visibility_detector: 0.4.0+2
  clear_all_notifications: 1.0.0
  flutter_downloader: 1.11.8
  android_path_provider: 0.3.0
  permission_handler: 11.0.1
  path_provider: 2.1.5
  disk_space_plus: 0.2.4
  android_id: 0.4.0
  network_info_plus: 6.1.4
  easy_localization: 3.0.7
  intl: 0.19.0
  path: 1.9.0
  uuid: 4.4.2
  collection: 1.18.0
  in_app_update: 4.2.3
  in_app_review: 2.0.10

  # Firebase
  firebase_core: 3.3.0
  firebase_crashlytics: 4.0.4
  firebase_analytics: 11.2.1
  firebase_performance: 0.10.0+4

  # TS SDK (without NFC reader)
  trust_vision_plugin:
    git:
      url: https://github.com/tsocial/tv_flutter_sdk_evo.git
      ref: 5851d0b26cd8ff1eaba8aa9abe738c2559292012

  # datadog plugins
  datadog_flutter_plugin: 2.10.2 # latest version is 2.11.0 which require gradle 8.6 (https://pub.dev/packages/datadog_flutter_plugin/changelog#2110), but our current gradle is 8.4
  datadog_tracking_http_client: 2.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: 5.0.0
  mocktail: 1.0.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/mock/

