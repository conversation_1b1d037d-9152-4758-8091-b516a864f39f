import 'package:dio/dio.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  // Base Response
  const int fakeStatusCode = 200;
  const String fakeHeadersKey = 'header_key';
  const String fakeHeaderValue = 'header_value';
  const String fakeRequestHeadersKey = 'request_header_key';
  const String fakeRequestHeaderValue = 'request_header_value';
  const String fakeRequestMethod = 'GET';
  const String fakeRequestOptionsPath = 'https://example.com';

  const String fakeDataKey = 'data_key';
  const String fakeDataValue = 'data_value';
  const String fakeUserMessage = 'user_message_value';
  const String fakeUserMessageTitle = 'user_message_title_value';

  const String fakeMessage = 'test message';
  const String fakeDateTime = '2021-12-01T12:00:00Z';
  const String fakeVerdict = 'success';

  final Map<String, dynamic> fakeData = <String, dynamic>{
    fakeDataKey: fakeDataValue,
    'user_message': fakeUserMessage,
    'user_message_title': fakeUserMessageTitle,
  };

  group('BaseEntity', () {
    test('fromJson constructor', () {
      final Map<String, dynamic> response = <String, dynamic>{
        'data': fakeData,
        'message': fakeMessage,
        'time': fakeDateTime,
        'verdict': fakeVerdict,
      };
      final Map<String, dynamic> headers = <String, dynamic>{fakeHeadersKey: fakeHeaderValue};
      final Map<String, dynamic> requestHeaders = <String, dynamic>{
        fakeRequestHeadersKey: fakeRequestHeaderValue
      };
      final RequestOptions requestOptions = RequestOptions(path: fakeRequestOptionsPath);

      final BaseResponse baseResponse = BaseResponse(
        statusCode: fakeStatusCode,
        response: response,
        headers: headers,
        requestHeaders: requestHeaders,
        requestMethod: fakeRequestMethod,
        requestOptions: requestOptions,
      );

      final BaseEntity baseEntity = BaseEntity.fromBaseResponse(baseResponse);

      expect(baseEntity.data, fakeData);
      expect(baseEntity.statusCode, CommonHttpClient.SUCCESS);
      expect(baseEntity.message, fakeMessage);
      expect(baseEntity.time, fakeDateTime);
      expect(baseEntity.verdict, fakeVerdict);
      expect(baseEntity.userMessage, fakeUserMessage);
      expect(baseEntity.userMessageTitle, fakeUserMessageTitle);
    });

    test('toJson method', () {
      final BaseEntity baseEntity = BaseEntity(
        data: <String, dynamic>{fakeDataKey: fakeDataValue},
        statusCode: CommonHttpClient.SUCCESS,
        message: fakeMessage,
        time: fakeDateTime,
        verdict: fakeVerdict,
      );

      final Map<String, dynamic> json = baseEntity.toJson();

      expect(json, <String, dynamic>{
        'data': <String, dynamic>{fakeDataKey: fakeDataValue},
        'status_code': CommonHttpClient.SUCCESS,
        'message': fakeMessage,
        'time': fakeDateTime,
        'verdict': fakeVerdict,
      });
    });
  });
}
