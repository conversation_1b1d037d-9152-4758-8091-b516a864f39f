import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/request/mobile_data_collection_request.dart';
import 'package:flutter_common_package/feature/data_collection/model/data_connection_type.dart';
import 'package:flutter_common_package/feature/data_collection/model/ip_address_info.dart';
import 'package:flutter_common_package/feature/data_collection/model/storage_info.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Test function toJson', () {
    test('Give data not null, should return not null json data', () {
      const String expectDataConnectionType = 'wifi';
      const double expectFreeStorage = 20;
      const double expectTotalStorage = 64;
      const String expectScreenResolution = '200 x 100';
      const String expectLocalDeviceIpAddress = 'fakeLocalDeviceIpAddress';

      final MobileDataCollectionRequest request = MobileDataCollectionRequest(
        dataConnectionType: <DataConnectionType>[DataConnectionType.wifi],
        storageInfo: StorageInfo(
          freeStorageInMegabytes: expectFreeStorage,
          totalStorageInMegabytes: expectTotalStorage,
        ),
        screenResolution: const Size(100, 200),
        ipAddressInfo: IpAddressInfo(
          localDeviceIpAddress: expectLocalDeviceIpAddress,
        ),
      );

      expect(request.toJson(), <String, dynamic>{
        'data_connection_type': <String>[expectDataConnectionType],
        'total_storage_of_device_in_mb': expectTotalStorage,
        'free_storage_of_device_in_mb': expectFreeStorage,
        'screen_resolution_of_device': expectScreenResolution,
        'local_device_ip_address': expectLocalDeviceIpAddress,
      });
    });

    test('Give data null, should return null in json data', () {
      final MobileDataCollectionRequest request = MobileDataCollectionRequest();

      expect(request.toJson(), <String, dynamic>{
        'data_connection_type': null,
        'total_storage_of_device_in_mb': null,
        'free_storage_of_device_in_mb': null,
        'screen_resolution_of_device': null,
        'local_device_ip_address': null,
      });
    });
  });
}
