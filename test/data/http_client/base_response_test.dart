import 'package:dio/dio.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const int fakeStatusCode = 200;
  const String fakeDataKey = 'data_key';
  const String fakeDataValue = 'data_value';
  const String fakeHeadersKey = 'header_key';
  const String fakeHeaderValue = 'header_value';
  const String fakeRequestHeadersKey = 'request_header_key';
  const String fakeRequestHeaderValue = 'request_header_value';
  const String fakeRequestMethod = 'GET';
  const String fakeRequestOptionsPath = 'https://example.com';

  group('BaseResponse', () {
    final Map<String, dynamic> response = <String, dynamic>{
      'data': <String, dynamic>{fakeDataKey: fakeDataValue}
    };
    final Map<String, dynamic> headers = <String, dynamic>{fakeHeadersKey: fakeHeaderValue};
    final Map<String, dynamic> requestHeaders = <String, dynamic>{
      fakeRequestHeadersKey: fakeRequestHeaderValue
    };
    final RequestOptions requestOptions = RequestOptions(path: fakeRequestOptionsPath);

    test('constructor and data getter', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: fakeStatusCode,
        response: response,
        headers: headers,
        requestHeaders: requestHeaders,
        requestMethod: fakeRequestMethod,
        requestOptions: requestOptions,
      );

      expect(baseResponse.statusCode, fakeStatusCode);
      expect(baseResponse.response?['data'][fakeDataKey], fakeDataValue);
      expect(baseResponse.headers?[fakeHeadersKey], fakeHeaderValue);
      expect(baseResponse.requestHeaders?[fakeRequestHeadersKey], fakeRequestHeaderValue);
      expect(baseResponse.requestMethod, fakeRequestMethod);
      expect(baseResponse.requestOptions?.path, fakeRequestOptionsPath);
      expect(baseResponse.data?[fakeDataKey], fakeDataValue);
    });

    test('toString', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: fakeStatusCode,
        response: response,
        headers: headers,
        requestHeaders: requestHeaders,
        requestMethod: fakeRequestMethod,
        requestOptions: requestOptions,
      );

      final String expectedString =
          'BaseResponse{ statusCode=$fakeStatusCode, response=${response.toString()}, headers=${headers.toString()}, requestHeaders=${requestHeaders.toString()}, requestMethod=$fakeRequestMethod, requestOptions=${requestOptions.toString()}}';

      expect(baseResponse.toString(), expectedString);
    });
  });
}
