import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_delay_widget.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/widgets/common_dialog_bottom_sheet_cta/common_dialog_bottom_sheet_cta.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/widgets/common_dialog_bottom_sheet_cta/common_dialog_bottom_sheet_cta_horizontal.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/widgets/common_dialog_bottom_sheet_cta/common_dialog_bottom_sheet_cta_vertical.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonButtonStyles extends Mock implements CommonButtonStyles {}

void main() {
  const ButtonListOrientation buttonListOrientation = ButtonListOrientation.verticalDown;
  const double ctaSpacing = 8;
  const EdgeInsets padding = EdgeInsets.all(16);
  const String textPositive = 'textPositive';
  const String textNegative = 'textNegative';

  final ButtonStyle defaultPositiveButtonStyle = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(Colors.red),
  );
  final ButtonStyle defaultNegativeButtonStyle = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(Colors.yellow),
  );

  final ButtonStyle positiveButtonStyle = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(Colors.orange),
  );
  final ButtonStyle negativeButtonStyle = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(Colors.green),
  );

  late CommonButtonStyles commonButtonStyles;

  setUpAll(() {
    getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
    commonButtonStyles = getIt<CommonButtonStyles>();

    registerFallbackValue(ButtonSize.large);

    when(() => commonButtonStyles.primary(any())).thenReturn(defaultPositiveButtonStyle);
    when(() => commonButtonStyles.tertiary(
          any(),
          isHasShadow: any(named: 'isHasShadow'),
        )).thenReturn(defaultNegativeButtonStyle);
  });

  testWidgets('Show CommonDialogBottomSheetCTA with only positive button',
      (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: CommonDialogBottomSheetCTA.create(
        buttonListOrientation: buttonListOrientation,
        ctaSpacing: ctaSpacing,
        padding: padding,
        textPositive: textPositive,
      ),
    ));

    /// Only show positive button with default style
    expect(find.text(textPositive), findsOneWidget);
    expect(find.byType(CommonButton), findsOneWidget);
    expect(
      find.byWidgetPredicate((Widget widget) {
        if (widget is CommonButton) {
          final Widget child = widget.child;
          final ButtonStyle style = widget.style;
          return child is Text && child.data == textPositive && style == defaultPositiveButtonStyle;
        }
        return false;
      }),
      findsOneWidget,
    );
  });

  testWidgets('Show CommonDialogBottomSheetCTA with positive & negative button',
      (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: CommonDialogBottomSheetCTA.create(
        buttonListOrientation: buttonListOrientation,
        ctaSpacing: ctaSpacing,
        padding: padding,
        textPositive: textPositive,
        textNegative: textNegative,
      ),
    ));

    expect(find.byType(CommonButton), findsNWidgets(2));

    /// Verify positive button
    expect(find.text(textPositive), findsOneWidget);
    expect(
      find.byWidgetPredicate((Widget widget) {
        if (widget is CommonButton) {
          final Widget child = widget.child;
          final ButtonStyle style = widget.style;
          return child is Text && child.data == textPositive && style == defaultPositiveButtonStyle;
        }
        return false;
      }),
      findsOneWidget,
    );

    /// Verify negative button
    expect(find.text(textPositive), findsOneWidget);
    expect(
      find.byWidgetPredicate((Widget widget) {
        if (widget is CommonButton) {
          final Widget child = widget.child;
          final ButtonStyle style = widget.style;
          return child is Text && child.data == textNegative && style == defaultNegativeButtonStyle;
        }
        return false;
      }),
      findsOneWidget,
    );
  });

  testWidgets('Show CommonDialogBottomSheetCTA with custom button style',
      (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: CommonDialogBottomSheetCTA.create(
        buttonListOrientation: buttonListOrientation,
        ctaSpacing: ctaSpacing,
        padding: padding,
        textPositive: textPositive,
        textNegative: textNegative,
        positiveButtonStyle: positiveButtonStyle,
        negativeButtonStyle: negativeButtonStyle,
      ),
    ));

    expect(find.byType(CommonButton), findsNWidgets(2));

    /// Verify positive button
    expect(find.text(textPositive), findsOneWidget);
    expect(
      find.byWidgetPredicate((Widget widget) {
        if (widget is CommonButton) {
          final Widget child = widget.child;
          final ButtonStyle style = widget.style;
          return child is Text && child.data == textPositive && style == positiveButtonStyle;
        }
        return false;
      }),
      findsOneWidget,
    );

    /// Verify negative button
    expect(find.text(textPositive), findsOneWidget);
    expect(
      find.byWidgetPredicate((Widget widget) {
        if (widget is CommonButton) {
          final Widget child = widget.child;
          final ButtonStyle style = widget.style;
          return child is Text && child.data == textNegative && style == negativeButtonStyle;
        }
        return false;
      }),
      findsOneWidget,
    );
  });

  group('Test CTA order', () {
    testWidgets('Show CommonDialogBottomSheetCTA with vertical down orientation',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CommonDialogBottomSheetCTA.create(
          buttonListOrientation: ButtonListOrientation.verticalDown,
          ctaSpacing: ctaSpacing,
          padding: padding,
          textPositive: textPositive,
          textNegative: textNegative,
        ),
      ));

      final Finder ctaFinder = find.byType(CommonDialogBottomSheetCTAVertical);
      expect(ctaFinder, findsOneWidget);
      final CommonDialogBottomSheetCTAVertical ctaWidget =
          tester.widget(ctaFinder) as CommonDialogBottomSheetCTAVertical;
      expect(ctaWidget.verticalDirection, VerticalDirection.down);
      expect(ctaWidget.ctaSpacing, ctaSpacing);
      expect(ctaWidget.padding, padding);
      expect(ctaWidget.textPositive, textPositive);
      expect(ctaWidget.textNegative, textNegative);
    });

    testWidgets('Show CommonDialogBottomSheetCTA with vertical up orientation',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CommonDialogBottomSheetCTA.create(
          buttonListOrientation: ButtonListOrientation.verticalUp,
          ctaSpacing: ctaSpacing,
          padding: padding,
          textPositive: textPositive,
          textNegative: textNegative,
        ),
      ));

      final Finder ctaFinder = find.byType(CommonDialogBottomSheetCTAVertical);
      expect(ctaFinder, findsOneWidget);
      final CommonDialogBottomSheetCTAVertical ctaWidget =
          tester.widget(ctaFinder) as CommonDialogBottomSheetCTAVertical;
      expect(ctaWidget.verticalDirection, VerticalDirection.up);
      expect(ctaWidget.ctaSpacing, ctaSpacing);
      expect(ctaWidget.padding, padding);
      expect(ctaWidget.textPositive, textPositive);
      expect(ctaWidget.textNegative, textNegative);
    });

    testWidgets('Show CommonDialogBottomSheetCTA with horizontal left to right orientation',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CommonDialogBottomSheetCTA.create(
          buttonListOrientation: ButtonListOrientation.horizontalLeftToRight,
          ctaSpacing: ctaSpacing,
          padding: padding,
          textPositive: textPositive,
          textNegative: textNegative,
        ),
      ));

      final Finder ctaFinder = find.byType(CommonDialogBottomSheetCTAHorizontal);
      expect(ctaFinder, findsOneWidget);
      final CommonDialogBottomSheetCTAHorizontal ctaWidget =
          tester.widget(ctaFinder) as CommonDialogBottomSheetCTAHorizontal;
      expect(ctaWidget.textDirection, TextDirection.ltr);
      expect(ctaWidget.ctaSpacing, ctaSpacing);
      expect(ctaWidget.padding, padding);
      expect(ctaWidget.textPositive, textPositive);
      expect(ctaWidget.textNegative, textNegative);
    });

    testWidgets('Show CommonDialogBottomSheetCTA with horizontal right to left orientation',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CommonDialogBottomSheetCTA.create(
          buttonListOrientation: ButtonListOrientation.horizontalRightToLeft,
          ctaSpacing: ctaSpacing,
          padding: padding,
          textPositive: textPositive,
          textNegative: textNegative,
        ),
      ));

      final Finder ctaFinder = find.byType(CommonDialogBottomSheetCTAHorizontal);
      expect(ctaFinder, findsOneWidget);
      final CommonDialogBottomSheetCTAHorizontal ctaWidget =
          tester.widget(ctaFinder) as CommonDialogBottomSheetCTAHorizontal;
      expect(ctaWidget.textDirection, TextDirection.rtl);
      expect(ctaWidget.ctaSpacing, ctaSpacing);
      expect(ctaWidget.padding, padding);
      expect(ctaWidget.textPositive, textPositive);
      expect(ctaWidget.textNegative, textNegative);
    });
  });

  testWidgets('Test Delay CTA button', (WidgetTester tester) async {
    const int delayInSeconds = 2;
    const Key positiveDelayOverlayKey = Key('positiveDelayOverlayKey');
    bool hasReceiveCallbackPositiveButtonClicked = false;

    await tester.pumpWidget(
      MediaQuery(
        data: const MediaQueryData(textScaler: TextScaler.linear(1.5)),
        child: MaterialApp(
          home: CommonDialogBottomSheetCTA.create(
            buttonListOrientation: buttonListOrientation,
            ctaSpacing: ctaSpacing,
            padding: padding,
            textPositive: textPositive,
            textNegative: textNegative,
            positiveDelayOverlay: (int seconds) => Text(
              '$seconds',
              key: positiveDelayOverlayKey,
            ),
            positiveDelayInSeconds: delayInSeconds,
            onClickPositive: () {
              hasReceiveCallbackPositiveButtonClicked = true;
            },
          ),
        ),
      ),
    );

    expect(find.byType(CommonButton), findsNWidgets(2));
    expect(find.byType(CommonDelayWidget), findsOneWidget);

    /// Verify positive button with delay overlay widget showed
    expect(find.byKey(positiveDelayOverlayKey), findsOneWidget);
    expect(find.text('2'), findsOneWidget);

    /// tap -> expect no callback is called
    await tester.tap(find.byType(CommonDelayWidget));
    expect(hasReceiveCallbackPositiveButtonClicked, false);

    await tester.pumpAndSettle(const Duration(seconds: 1));
    expect(find.text('1'), findsOneWidget);

    await tester.pumpAndSettle(const Duration(seconds: 1));

    /// Verify positive button with button text (after delayed)
    expect(find.text(textPositive), findsOneWidget);

    /// tap -> expect callback is called
    await tester.tap(find.byType(CommonDelayWidget));
    expect(hasReceiveCallbackPositiveButtonClicked, true);
  });
}
