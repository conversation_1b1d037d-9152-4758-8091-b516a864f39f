import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/widgets/common_dialog_bottom_sheet_cta/common_dialog_bottom_sheet_cta_horizontal.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonButtonStyles extends Mock implements CommonButtonStyles {}

void main() {
  const double ctaSpacing = 8;
  const EdgeInsets padding = EdgeInsets.all(16);
  const String textPositive = 'textPositive';
  const String textNegative = 'textNegative';
  final ButtonStyle defaultPositiveButtonStyle = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(Colors.red),
  );
  final ButtonStyle defaultNegativeButtonStyle = ButtonStyle(
    backgroundColor: WidgetStateProperty.all(Colors.yellow),
  );

  late CommonButtonStyles commonButtonStyles;

  setUpAll(() {
    getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
    commonButtonStyles = getIt<CommonButtonStyles>();

    registerFallbackValue(ButtonSize.large);

    when(() => commonButtonStyles.primary(any())).thenReturn(defaultPositiveButtonStyle);
    when(() => commonButtonStyles.tertiary(
          any(),
          isHasShadow: any(named: 'isHasShadow'),
        )).thenReturn(defaultNegativeButtonStyle);
  });

  testWidgets('Show CommonDialogBottomSheetCTAHorizontal with horizontal left to right orientation',
      (WidgetTester tester) async {
    await tester.pumpWidget(const MaterialApp(
      home: CommonDialogBottomSheetCTAHorizontal(
        ctaSpacing: ctaSpacing,
        padding: padding,
        textPositive: textPositive,
        textNegative: textNegative,
        textDirection: TextDirection.ltr,
      ),
    ));

    final Finder commonButtonsInRow = find.descendant(
      of: find.byWidgetPredicate((Widget widget) {
        return widget is Row && widget.textDirection == TextDirection.ltr;
      }),
      matching: find.byType(CommonButton),
    );
    expect(commonButtonsInRow, findsNWidgets(2));
  });

  testWidgets('Show CommonDialogBottomSheetCTAHorizontal with horizontal right to left orientation',
      (WidgetTester tester) async {
    await tester.pumpWidget(const MaterialApp(
      home: CommonDialogBottomSheetCTAHorizontal(
        ctaSpacing: ctaSpacing,
        padding: padding,
        textPositive: textPositive,
        textNegative: textNegative,
        textDirection: TextDirection.rtl,
      ),
    ));

    final Finder commonButtonsInRow = find.descendant(
      of: find.byWidgetPredicate((Widget widget) {
        return widget is Row && widget.textDirection == TextDirection.rtl;
      }),
      matching: find.byType(CommonButton),
    );
    expect(commonButtonsInRow, findsNWidgets(2));
  });
}
