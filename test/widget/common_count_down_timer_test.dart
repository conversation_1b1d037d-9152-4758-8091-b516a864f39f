import 'package:flutter_common_package/widget/count_down_timer/common_count_down_timer.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockOnTick extends Mock {
  void call(int seconds);
}

class MockOnFinish extends Mock {
  void call();
}

void main() {
  const int countDownSeconds = 5;
  late MockOnTick mockOnTick;
  late MockOnFinish mockOnFinish;
  late CommonCountdownTimer commonCountdownTimer;

  setUpAll(() {
    mockOnTick = MockOnTick();
    mockOnFinish = MockOnFinish();
  });

  setUp(() {
    commonCountdownTimer = CommonCountdownTimer(
      countDownSeconds: countDownSeconds,
      onTick: mockOnTick.call,
      onFinished: mockOnFinish.call,
    );
  });

  Future<void> delayed({required int seconds}) async {
    await Future<void>.delayed(Duration(seconds: seconds));
  }

  group('test class CommonCountdownTimer', () {
    test('verify start() method', () async {
      commonCountdownTimer.start();
      await delayed(seconds: countDownSeconds + 1);
      verify(() => mockOnTick.call(4)).called(1);
      verify(() => mockOnTick.call(3)).called(1);
      verify(() => mockOnTick.call(2)).called(1);
      verify(() => mockOnTick.call(1)).called(1);
      verify(() => mockOnFinish.call()).called(1);
      expect(commonCountdownTimer.countDownTimer.isActive, isFalse);
    });

    test('verify stop() method', () async {
      commonCountdownTimer.start();
      await delayed(seconds: 2);
      verify(() => mockOnTick.call(4)).called(1);

      commonCountdownTimer.stop();
      expect(commonCountdownTimer.countDownSeconds, 0);
      verifyNever(() => mockOnTick.call(any()));
      verifyNever(() => mockOnFinish.call());
      expect(commonCountdownTimer.countDownTimer.isActive, isFalse);
    });
  });
}
