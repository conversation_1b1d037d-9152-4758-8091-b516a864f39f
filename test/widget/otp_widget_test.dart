import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/http_client/dio_request_option_mapper.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/util/otp_auto_fill/sms_otp_auto_fill_impl.dart';
import 'package:flutter_common_package/widget/back_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/otp/count_down_and_resend_otp_widget.dart';
import 'package:flutter_common_package/widget/otp/otp_widget.dart';
import 'package:flutter_common_package/widget/otp/sub_title_and_phone_number_otp.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:otp_autofill/otp_autofill.dart';

import '../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockOnResendOtp extends Mock {
  void call();
}

void main() {
  const String fakePhoneNumber = '**********';
  const int fakeResendInSec = 30;
  const String fakeErrorMessage = 'fake_error';
  const String fakeOtpLimit = 'fake_otp_limit';
  const String fakeOtpTitle = 'fake_otp_title';
  const String fakeLeading = 'fake_leading';

  const Widget fakeLeadingWidget = Text(fakeLeading);

  late CommonColors mockCommonColors;
  late CommonTextStyles mockCommonTextStyles;
  late BuildContext mockBuildContext;
  late CommonImageProvider commonImageProvider;
  late MockOnResendOtp mockOnResendOtp;

  void initGetItRegister() {
    getIt.registerFactory<OtpAutoFill>(() => SmsOtpAutoFillImpl(OTPInteractor()));

    getIt.registerLazySingleton<DevicePlatform>(() => DevicePlatformImp());

    getIt.registerLazySingleton<LoggingRepo>(
      () => LoggingRepoImpl(commonHttpClient: getIt.get<CommonHttpClient>()),
    );

    getIt.registerLazySingleton<CommonHttpClient>(
      () => DioClientImpl(
        getIt.get<Dio>(),
        dioRequestOptionMapper: DioRequestOptionMapper(),
      ),
    );

    getIt.registerLazySingleton<Dio>(() => Dio());

    mockBuildContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockBuildContext);

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterMockImageProvider();
  }

  void initWhenCalled() {
    when(() => mockCommonTextStyles.h300(color: any(named: 'color'))).thenAnswer((_) {
      return const TextStyle(color: Colors.black);
    });

    when(() => mockCommonTextStyles.h600(any())).thenAnswer((_) {
      return const TextStyle(color: Colors.black);
    });

    when(() => mockCommonTextStyles.h700(any())).thenAnswer((_) {
      return const TextStyle(color: Colors.black);
    });

    when(() => mockCommonTextStyles.bodyLarge(any())).thenAnswer((_) {
      return const TextStyle(color: Colors.black);
    });
    when(() => mockCommonTextStyles.bodySmall(color: any(named: 'color'))).thenAnswer((_) {
      return const TextStyle(color: Colors.black);
    });

    when(() => mockCommonColors.background).thenReturn(Colors.white);
    when(() => mockCommonColors.foreground).thenReturn(Colors.white);
    when(() => mockCommonColors.textActive).thenReturn(Colors.black);
    when(() => mockCommonColors.error).thenReturn(Colors.red);
    when(() => mockCommonColors.textPassive).thenReturn(Colors.grey);
    when(() => mockCommonColors.highlighted).thenReturn(Colors.green);
    when(() => mockCommonColors.inputFocusedColor).thenReturn(Colors.white);
    when(() => mockCommonColors.inputUnfocusedColor).thenReturn(Colors.black);

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  }

  void registerFallbackColor() {
    registerFallbackValue(Colors.black);
    registerFallbackValue(Colors.white);
    registerFallbackValue(Colors.red);
    registerFallbackValue(Colors.grey);
    registerFallbackValue(Colors.green);
  }

  setUpAll(() {
    registerFallbackColor();

    initGetItRegister();

    mockCommonColors = getIt.get<CommonColors>();

    mockCommonTextStyles = getIt.get<CommonTextStyles>();

    commonImageProvider = getIt.get<CommonImageProvider>();

    mockOnResendOtp = MockOnResendOtp();
    initWhenCalled();
  });

  group('Test OtpWidget', () {
    testWidgets('verify OtpWidget with default value', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OtpWidget(
              phoneNumber: fakePhoneNumber,
              resendInSec: fakeResendInSec,
              onSubmit: (String otp) {},
              errorText: fakeErrorMessage,
            ),
          ),
        ),
      );

      final Finder titleFinder = find.text(CommonStrings.otpTitle);
      expect(titleFinder, findsOneWidget);

      final Text titleText = widgetTester.widget(titleFinder);
      expect(titleText.style, const TextStyle(color: Colors.black));
      verify(() => mockCommonTextStyles.h600(Colors.black)).called(1);

      expect(find.byType(CommonBackButton), findsOneWidget);
      expect(find.byType(SubTitleAndPhoneNumberOtpWidget), findsOneWidget);
      expect(find.byType(CommonPinCode), findsOneWidget);
      expect(find.byType(CountDownAndResendOtpWidget), findsOneWidget);

      final Finder textErrorOtpLimitFinder = find.text(fakeOtpLimit);
      expect(textErrorOtpLimitFinder, findsNothing);

      //pump other widget to dispose current timer
      await widgetTester.pumpWidget(Container());
      widgetTester.pumpAndSettle();
    });

    testWidgets('verify OtpWidget with textOtpLimit != null', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OtpWidget(
              phoneNumber: fakePhoneNumber,
              resendInSec: fakeResendInSec,
              onSubmit: (String otp) {},
              errorText: fakeErrorMessage,
              textOtpLimit: fakeOtpLimit,
            ),
          ),
        ),
      );

      final Finder titleFinder = find.text(CommonStrings.otpTitle);
      expect(titleFinder, findsOneWidget);

      final Text titleText = widgetTester.widget(titleFinder);
      expect(titleText.style, const TextStyle(color: Colors.black));
      verify(() => mockCommonTextStyles.h600(Colors.black)).called(1);

      expect(find.byType(CommonBackButton), findsOneWidget);
      expect(find.byType(SubTitleAndPhoneNumberOtpWidget), findsNothing);
      expect(find.byType(CommonPinCode), findsNothing);
      expect(find.byType(CountDownAndResendOtpWidget), findsNothing);

      final Finder textErrorOtpLimitFinder = find.text(fakeOtpLimit);
      expect(textErrorOtpLimitFinder, findsOneWidget);

      final Text textOtpLimit = widgetTester.widget(textErrorOtpLimitFinder);
      expect(textOtpLimit.style, const TextStyle(color: Colors.black));
      verify(() => mockCommonTextStyles.bodyLarge(Colors.red)).called(1);
    });

    testWidgets('verify OtpWidget with custom value and textOtpLimit == null',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OtpWidget(
              phoneNumber: fakePhoneNumber,
              resendInSec: fakeResendInSec,
              onSubmit: (String otp) {},
              errorText: fakeErrorMessage,
              onResendOtp: mockOnResendOtp.call,
              leading: fakeLeadingWidget,
              textOtpTitle: fakeOtpTitle,
            ),
          ),
        ),
      );

      final Finder leadingFinder = find.text(fakeLeading);
      expect(leadingFinder, findsOneWidget);

      final Finder titleFinder = find.text(fakeOtpTitle);
      expect(titleFinder, findsOneWidget);

      final Text titleText = widgetTester.widget(titleFinder);
      expect(titleText.style, const TextStyle(color: Colors.black));
      verify(() => mockCommonTextStyles.h600(Colors.black)).called(1);

      expect(find.byType(SubTitleAndPhoneNumberOtpWidget), findsOneWidget);
      expect(find.byType(CommonPinCode), findsOneWidget);
      expect(find.byType(CountDownAndResendOtpWidget), findsOneWidget);

      final Finder textErrorOtpLimitFinder = find.text(fakeOtpLimit);
      expect(textErrorOtpLimitFinder, findsNothing);

      //pump other widget to dispose current timer
      await widgetTester.pumpWidget(Container());
      widgetTester.pumpAndSettle();
    });
  });
}
