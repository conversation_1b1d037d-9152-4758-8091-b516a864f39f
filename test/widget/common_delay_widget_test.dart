import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_delay_widget.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('CommonDelayWidget countdown test', (WidgetTester tester) async {
    // Define the duration for the countdown
    const int durationInSeconds = 2;

    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: CommonDelayWidget(
          durationInSeconds: durationInSeconds,
          builder: (bool isDelaying, int remainingTimeInSeconds) {
            return Text(
              isDelaying ? 'Countdown: $remainingTimeInSeconds' : 'Countdown Complete',
            );
          },
        ),
      ),
    );

    // Verify initial state
    expect(find.text('Countdown: 2'), findsOneWidget);
    expect(find.text('Countdown Complete'), findsNothing);

    // Advance time by 1 second
    await tester.pump(const Duration(seconds: 1));

    // Verify state after 1 second
    expect(find.text('Countdown: 1'), findsOneWidget);
    expect(find.text('Countdown Complete'), findsNothing);

    // Advance time by another second
    await tester.pump(const Duration(seconds: 1));

    // Verify final state
    expect(find.text('Countdown: 0'), findsNothing);
    expect(find.text('Countdown Complete'), findsOneWidget);
  });
}
