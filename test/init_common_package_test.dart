// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

/// This file contains tests for the module initialization system in the common package.
///
/// The tests verify that:
/// 1. Modules can be registered and initialized correctly
/// 2. Selective initialization of modules works as expected
/// 3. Module dependencies are resolved correctly
/// 4. Error handling works properly for invalid module names
/// 5. Helper functions for module status reporting work correctly
/// 6. Custom module registration and initialization
///
/// The test suite follows the AAA pattern (Arrange-Act-Assert) and includes:
/// - Unit tests for individual functions
/// - Integration tests for the complete initialization flow
/// - Parameterized tests for different initialization scenarios
/// - Edge case and error handling tests

import 'dart:async';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/module/common_package_modules/analytics_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/data_collection_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/device_info_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/ekyc_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/network_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/notification_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/ui_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/utility_module.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/base/module/module_registry.dart';
import 'package:flutter_common_package/base/module/module_registry_builder.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/feature/data_collection/device_identifier/android_device_identifier/android_device_identifier.dart';
import 'package:flutter_common_package/feature/data_collection/device_identifier/device_identifier.dart';
import 'package:flutter_common_package/feature/server_logging/analytics_service_impl.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'base/module/mocks/mock_modules.dart';

// Mock classes for testing
class MockDataCollector extends Mock implements DataCollector {}
class MockDeviceInfoPluginWrapper extends Mock implements DeviceInfoPluginWrapper {}
class MockModuleRegistry extends Mock implements ModuleRegistry {}
class MockModuleRegistryBuilder extends Mock implements ModuleRegistryBuilder {}
class MockFeatureModule extends Mock implements FeatureModule {}
class FakeFeatureModule extends Fake implements FeatureModule {}
class MockFlavorConfig extends Mock implements FlavorConfig {}
class MockCommonFlavorValues extends Mock implements CommonFlavorValues {}
class MockAnalyticsServiceImpl extends Mock implements AnalyticsServiceImpl {}
class MockCoreModule extends Mock implements CoreModule {}
class MockNetworkModule extends Mock implements NetworkModule {}
class MockAnalyticsModule extends Mock implements AnalyticsModule {}
class MockDeviceInfoModule extends Mock implements DeviceInfoModule {}
class MockDataCollectionModule extends Mock implements DataCollectionModule {}
class MockNotificationModule extends Mock implements NotificationModule {}
class MockUiModule extends Mock implements UiModule {}
class MockUtilityModule extends Mock implements UtilityModule {}
class MockEkycModule extends Mock implements EkycModule {}
class MockLoggingRepo extends Mock implements LoggingRepo {}
class MockDio extends Mock implements Dio {
  @override
  BaseOptions get options => BaseOptions();
}
class MockCommonHttpClient extends Mock implements CommonHttpClient {}
class MockDeviceIdentifier extends Mock implements DeviceIdentifier {}
class MockAndroidDeviceIdentifier extends Mock implements AndroidDeviceIdentifier {}

/// Shared test fixture to reduce setup code duplication
///
/// This class provides a centralized way to set up mocks and test dependencies
/// for testing the module initialization system. It handles creating mock objects,
/// configuring their behavior, and registering them with GetIt.
///
/// The fixture includes methods for preparing the test environment and initializing
/// modules in a controlled way that isolates tests from each other.
///
/// Benefits of this approach:
/// - Reduces code duplication across test cases
/// - Ensures consistent test setup
/// - Makes tests more maintainable
/// - Isolates tests from each other by resetting GetIt between tests
class CommonTestFixture {
  // Mock dependencies used across tests
  late DataCollector mockDataCollector;
  late DevicePlatform mockDevicePlatform;
  late DeviceInfoPluginWrapper mockDeviceInfoPluginWrapper;
  late LoggingRepo mockLoggingRepo;
  late Connectivity mockConnectivity;
  late NetworkManager mockNetworkManager;
  late MockDio mockDio;
  late MockCommonHttpClient mockCommonHttpClient;
  late MockDeviceIdentifier mockDeviceIdentifier;
  late MockAndroidDeviceIdentifier mockAndroidDeviceIdentifier;

  // Reference to the module registry for assertions
  late ModuleRegistry testModuleRegistry;
  late MockModuleRegistry mockModuleRegistry;
  late MockModuleRegistryBuilder mockBuilder;
  late MockCoreModule mockCoreModule;
  late MockNetworkModule mockNetworkModule;
  late MockAnalyticsModule mockAnalyticsModule;
  late MockDeviceInfoModule mockDeviceInfoModule;
  late MockDataCollectionModule mockDataCollectionModule;
  late MockNotificationModule mockNotificationModule;
  late MockUiModule mockUiModule;
  late MockUtilityModule mockUtilityModule;
  late MockEkycModule mockEkycModule;

  CommonTestFixture() {
    _setupMocks();
  }

  void _setupMocks() {
    // Create mocks
    mockDataCollector = MockDataCollector();
    mockDevicePlatform = MockDevicePlatform();
    mockDeviceInfoPluginWrapper = MockDeviceInfoPluginWrapper();
    mockLoggingRepo = MockLoggingRepo();
    mockConnectivity = MockConnectivity();
    mockNetworkManager = MockNetworkManager();
    mockDio = MockDio();
    mockCommonHttpClient = MockCommonHttpClient();
    mockModuleRegistry = MockModuleRegistry();
    mockBuilder = MockModuleRegistryBuilder();
    mockCoreModule = MockCoreModule();
    mockNetworkModule = MockNetworkModule();
    mockAnalyticsModule = MockAnalyticsModule();
    mockDeviceInfoModule = MockDeviceInfoModule();
    mockDataCollectionModule = MockDataCollectionModule();
    mockNotificationModule = MockNotificationModule();
    mockUiModule = MockUiModule();
    mockUtilityModule = MockUtilityModule();
    mockEkycModule = MockEkycModule();
    mockDeviceIdentifier = MockDeviceIdentifier();
    mockAndroidDeviceIdentifier = MockAndroidDeviceIdentifier();

    // Setup mock behaviors
    when(() => mockDataCollector.getDeviceId()).thenAnswer((_) async => 'test-device-id');
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);
    when(() => mockDeviceInfoPluginWrapper.getDeviceModel()).thenReturn('Test Device');
    when(() => mockDeviceInfoPluginWrapper.getPlatformName()).thenReturn('Android');
    when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenReturn('11.0');
    when(() => mockDeviceInfoPluginWrapper.getAndroidBuildNumber()).thenReturn('RQ3A.211001.001');
    when(() => mockConnectivity.checkConnectivity())
        .thenAnswer((_) async => <ConnectivityResult>[ConnectivityResult.wifi]);
    when(() => mockDeviceIdentifier.getDeviceId()).thenAnswer((_) async => 'test-device-id');
    when(() => mockAndroidDeviceIdentifier.getAndroidId()).thenAnswer((_) async => 'test-android-id');

    // Setup builder and registry behaviors
    when(() => mockBuilder.register(any(), source: any(named: 'source')))
        .thenReturn(mockBuilder);
    when(() => mockBuilder.build()).thenReturn(mockModuleRegistry);
    when(() => mockModuleRegistry.isModuleRegistered(any())).thenReturn(true);
    when(() => mockModuleRegistry.initializeAllRegisteredModules()).thenAnswer((_) async {});
    when(() => mockModuleRegistry.initializeSpecificModules(any())).thenAnswer((_) async {});
    when(() => mockModuleRegistry.initializeNoModules()).thenAnswer((_) async {});

    // Setup module behaviors
    when(() => mockCoreModule.name).thenReturn(CommonPackageModuleNames.core);
    when(() => mockNetworkModule.name).thenReturn(CommonPackageModuleNames.network);
    when(() => mockAnalyticsModule.name).thenReturn(CommonPackageModuleNames.analytics);
    when(() => mockDeviceInfoModule.name).thenReturn(CommonPackageModuleNames.deviceInfo);
    when(() => mockDataCollectionModule.name).thenReturn(CommonPackageModuleNames.dataCollection);
    when(() => mockNotificationModule.name).thenReturn(CommonPackageModuleNames.notification);
    when(() => mockUiModule.name).thenReturn(CommonPackageModuleNames.ui);
    when(() => mockUtilityModule.name).thenReturn(CommonPackageModuleNames.utility);
    when(() => mockEkycModule.name).thenReturn(CommonPackageModuleNames.ekyc);
  }

  /// Prepares the test environment with mock modules
  ///
  /// This method:
  /// 1. Resets the GetIt service locator
  /// 2. Registers essential mock dependencies
  /// 3. Creates mock modules with configured behaviors
  /// 4. Registers the mock modules with a real ModuleRegistry
  /// 5. Builds the registry for use in tests
  ///
  /// After calling this method, the test can initialize modules selectively
  /// using the moduleRegistry instance. This is particularly useful for testing
  /// module initialization, dependency resolution, and error handling.
  ///
  /// Parameters:
  /// - locale: Optional locale to use for localized network requests.
  ///
  /// Returns a Future that completes when the test environment is ready.
  Future<void> prepareModulesForTesting({Locale? locale}) async {
    // Reset GetIt and create a new ModuleRegistry
    getIt.reset();

    // Register essential dependencies using the helper method
    _registerEssentialDependencies();

    // Create mock modules with configured behaviors
    final MockNetworkModule mockNetworkModule = MockNetworkModule();
    when(() => mockNetworkModule.name).thenReturn(CommonPackageModuleNames.network);
    when(() => mockNetworkModule.dependencies).thenReturn(<Type>[
      Connectivity,
      NetworkManager,
      Dio,
      CommonHttpClient,
    ]);
    when(() => mockNetworkModule.register(any())).thenAnswer((_) async {});

    final MockAnalyticsModule mockAnalyticsModule = MockAnalyticsModule();
    when(() => mockAnalyticsModule.name).thenReturn(CommonPackageModuleNames.analytics);
    when(() => mockAnalyticsModule.dependencies).thenReturn(<Type>[
      LoggingRepo,
      EventTrackingUtils,
      FirebaseAnalyticsWrapper,
      CommonNavigatorObserver,
      DatadogLogger,
      AnalyticsServiceImpl,
    ]);
    when(() => mockAnalyticsModule.register(any())).thenAnswer((_) async {});

    // Create mock device info module to prevent platform plugin exceptions
    final MockDeviceInfoModule mockDeviceInfoModule = MockDeviceInfoModule();
    when(() => mockDeviceInfoModule.name).thenReturn(CommonPackageModuleNames.deviceInfo);
    when(() => mockDeviceInfoModule.dependencies).thenReturn(<Type>[
      DeviceInfoPluginWrapper,
      DevicePlatform,
    ]);
    when(() => mockDeviceInfoModule.register(any())).thenAnswer((_) async {});

    // Create mock data collection module to prevent platform plugin exceptions
    final MockDataCollectionModule mockDataCollectionModule = MockDataCollectionModule();
    when(() => mockDataCollectionModule.name).thenReturn(CommonPackageModuleNames.dataCollection);
    when(() => mockDataCollectionModule.dependencies).thenReturn(<Type>[
      DataCollector,
    ]);
    when(() => mockDataCollectionModule.register(any())).thenAnswer((_) async {});

    // Create a registry builder
    final ModuleRegistryBuilder builder = ModuleRegistry.builder(getIt);

    // Register mock modules and real UI module
    const String source = 'test';
    builder.register(mockNetworkModule, source: source);
    builder.register(mockAnalyticsModule, source: source);
    builder.register(mockDeviceInfoModule, source: source);
    builder.register(mockDataCollectionModule, source: source);
    builder.register(UiModule(), source: source);

    // Register a core module for dependency resolution testing
    final MockCoreModule mockCoreModule = MockCoreModule();
    when(() => mockCoreModule.name).thenReturn(CommonPackageModuleNames.core);
    when(() => mockCoreModule.dependencies).thenReturn(<Type>[
      PackageInfo,
    ]);
    when(() => mockCoreModule.register(any())).thenAnswer((_) async {});
    builder.register(mockCoreModule, source: source);

    // Build the registry
    moduleRegistry = builder.build();

    // Assign to testModuleRegistry for test assertions
    testModuleRegistry = moduleRegistry;
  }

  /// Registers essential dependencies for testing
  ///
  /// This helper method registers all the essential mock dependencies that are
  /// required by most modules. It's used to reduce code duplication across tests.
  ///
  /// Call this method before initializing modules to ensure all required
  /// dependencies are available.
  void _registerEssentialDependencies() {
    getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);
    getIt.registerSingleton<DeviceInfoPluginWrapper>(mockDeviceInfoPluginWrapper);
    getIt.registerSingleton<LoggingRepo>(mockLoggingRepo);
    getIt.registerSingleton<Connectivity>(mockConnectivity);
    getIt.registerSingleton<NetworkManager>(mockNetworkManager);
    getIt.registerSingleton<DeviceIdentifier>(mockDeviceIdentifier);
    getIt.registerSingleton<AndroidDeviceIdentifier>(mockAndroidDeviceIdentifier);

    // Register PackageInfo asynchronously because DioFactory expects it this way
    final packageInfo = PackageInfo(
      appName: 'Test App',
      packageName: 'com.test.app',
      version: '1.0.0',
      buildNumber: '1',
      buildSignature: 'test',
    );
    getIt.registerSingletonAsync<PackageInfo>(() async => packageInfo);

    // Register Dio and CommonHttpClient after waiting for async dependencies to be ready
    getIt.registerSingletonAsync<Dio>(() async {
      // Wait for PackageInfo to be registered before creating Dio
      await getIt.isReady<PackageInfo>();
      return mockDio;
    });

    getIt.registerSingletonAsync<CommonHttpClient>(() async {
      // Wait for Dio to be registered before creating CommonHttpClient
      await getIt.isReady<Dio>();
      return mockCommonHttpClient;
    });
  }

  /// Prepares the test environment and initializes the common package
  ///
  /// This method sets up the test environment with mock dependencies and then
  /// calls the real initCommonPackage function with the specified parameters.
  ///
  /// Parameters:
  /// - features: Optional list of module names to initialize. If null, all modules are initialized.
  /// - locale: Optional locale to use for localized network requests.
  ///
  /// This approach uses the real code instead of test-specific methods, following
  /// the best practice of avoiding test-specific methods. It also ensures that
  /// each test starts with a clean state by resetting GetIt.
  ///
  /// Returns a Future that completes when initialization is done.
  Future<void> prepareAndInitCommonPackage({
    List<String>? features,
    Locale? locale,
  }) async {
    // Reset GetIt to ensure a clean state
    getIt.reset();

    // Register essential mock dependencies
    _registerEssentialDependencies();

    // Register mock modules to prevent platform plugin exceptions
    final mockModules = <FeatureModule>[
      mockCoreModule,
      mockNetworkModule,
      mockAnalyticsModule,
      mockDeviceInfoModule,
      mockDataCollectionModule,
      mockNotificationModule,
      mockUiModule,
      mockUtilityModule,
      mockEkycModule,
    ];

    // Setup mock behaviors for all modules
    for (final module in mockModules) {
      when(() => module.register(any())).thenAnswer((_) async {});
    }

    // Create a registry builder with our mocks
    final builder = ModuleRegistry.builder(getIt);
    const source = 'test';

    // Register all mock modules
    for (final FeatureModule module in mockModules) {
      builder.register(module, source: source);
    }

    // Build the registry
    moduleRegistry = builder.build();

    // Initialize modules based on features parameter
    if (features == null) {
      await moduleRegistry.initializeAllRegisteredModules();
    } else if (features.isEmpty) {
      await moduleRegistry.initializeNoModules();
    } else {
      await moduleRegistry.initializeSpecificModules(features);
    }

    // Store the moduleRegistry for test assertions
    testModuleRegistry = moduleRegistry;
  }

  /// Registers all standard dependencies for integration tests
  ///
  /// This method registers the standard set of mock dependencies that are required
  /// by most modules. It's used to set up a consistent test environment for
  /// integration tests that need to interact with multiple modules.
  ///
  /// The method registers mocks for:
  /// - DevicePlatform
  /// - DeviceInfoPluginWrapper
  /// - LoggingRepo
  /// - Connectivity
  /// - NetworkManager
  /// - PackageInfo
  void registerStandardDependencies() {
    getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);
    getIt.registerSingleton<DeviceInfoPluginWrapper>(mockDeviceInfoPluginWrapper);
    getIt.registerSingleton<LoggingRepo>(mockLoggingRepo);
    getIt.registerSingleton<Connectivity>(mockConnectivity);
    getIt.registerSingleton<NetworkManager>(mockNetworkManager);
    getIt.registerSingleton<DeviceIdentifier>(mockDeviceIdentifier);
    getIt.registerSingleton<AndroidDeviceIdentifier>(mockAndroidDeviceIdentifier);
    getIt.registerSingleton<PackageInfo>(PackageInfo(
      appName: 'Test App',
      packageName: 'com.test.app',
      version: '1.0.0',
      buildNumber: '1',
      buildSignature: 'test',
    ));
  }
}

void main() {
  late CommonTestFixture fixture;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(GetIt.instance);
    registerFallbackValue(FakeFeatureModule());
    FlavorConfig(
      flavor: 'dev',
      values: CommonFlavorValues(
        baseUrl: 'https://test-api.example.com',
        initializeFirebaseSdk: false,
        oneSignalAppId: null,
      ),
    );
  });

  setUp(() {
    getIt.reset();
    fixture = CommonTestFixture();
  });

  tearDown(() {
    getIt.reset();
  });

  group('initCommonPackage public API', () {
    test('initCommonPackage initializes all modules by default', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage();
      final registeredModules = getRegisteredModules();
      final initializedModules = getInitializedModules();
      expect(initializedModules, containsAll(registeredModules));
    });

    test('initCommonPackage initializes no modules if features is empty', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage(features: <String>[]);
      expect(getInitializedModules(), isEmpty);
    });

    test('initCommonPackage initializes only specified modules', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage(features: [CommonPackageModuleNames.network]);
      expect(isModuleInitialized(CommonPackageModuleNames.network), isTrue);
      expect(isModuleInitialized(CommonPackageModuleNames.analytics), isFalse);
    });

    test('getRegisteredModules returns all registered modules', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage();
      final registeredModules = getRegisteredModules();
      expect(registeredModules, isNotEmpty);
    });

    test('getInitializedModules returns all initialized modules', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage();
      final initializedModules = getInitializedModules();
      expect(initializedModules, isNotEmpty);
    });

    test('isModuleInitialized returns correct status', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage(features: [CommonPackageModuleNames.network]);
      expect(isModuleInitialized(CommonPackageModuleNames.network), isTrue);
      expect(isModuleInitialized(CommonPackageModuleNames.analytics), isFalse);
    });

    test('isModuleRegistered returns correct status', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage();
      expect(isModuleRegistered(CommonPackageModuleNames.network), isTrue);
      expect(isModuleRegistered('non_existent_module'), isFalse);
    });

    test('initializeAllRegisteredModules initializes all modules', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage(features: []);
      await initializeAllRegisteredModules();
      final registeredModules = getRegisteredModules();
      final initializedModules = getInitializedModules();
      expect(initializedModules, containsAll(registeredModules));
    });

    test('initializeSpecificModules initializes only specified modules', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage(features: []);
      await initializeSpecificModules([CommonPackageModuleNames.network]);
      expect(isModuleInitialized(CommonPackageModuleNames.network), isTrue);
      expect(isModuleInitialized(CommonPackageModuleNames.analytics), isFalse);
    });

    test('initializeNoModules does not initialize any modules', () async {
      await fixture.prepareModulesForTesting();
      await initCommonPackage(features: []);
      await initializeNoModules();
      expect(getInitializedModules(), isEmpty);
    });

    test('registerCustomModule registers a custom module', () async {
      await initCommonPackage(features: []);
      final testModule = MockFeatureModule();
      when(() => testModule.name).thenReturn('test_custom_module');
      when(() => testModule.dependencies).thenReturn(<Type>[]);
      when(() => testModule.register(any())).thenAnswer((_) async {});
      registerCustomModule(testModule, source: 'test_source');
      expect(isModuleRegistered('test_custom_module'), isTrue);
      expect(isModuleInitialized('test_custom_module'), isFalse);
    });

    test('registerAndInitializeCustomModule registers and initializes a custom module', () async {
      await initCommonPackage(features: []);
      final testModule = MockFeatureModule();
      when(() => testModule.name).thenReturn('test_custom_module');
      when(() => testModule.dependencies).thenReturn(<Type>[]);
      when(() => testModule.register(any())).thenAnswer((_) async {});
      await registerAndInitializeCustomModule(testModule, source: 'test_source');
      expect(isModuleRegistered('test_custom_module'), isTrue);
      expect(isModuleInitialized('test_custom_module'), isTrue);
    });

    test('checkModuleRegistryInitialized does not throw when registry is initialized', () async {
      // First initialize the module registry
      await fixture.prepareModulesForTesting();

      // Should not throw when the registry is properly initialized
      expect(() => checkModuleRegistryInitialized(), returnsNormally);
    });
  });
}
