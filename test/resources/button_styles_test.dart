import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonTextStyles extends Mock implements CommonTextStyles {}

class MockCommonColors extends Mock implements CommonColors {}

class MockCommonButtonDimensions extends Mock implements CommonButtonDimensions {}

void _expectColors(
  ButtonStyle style, {
  required Color expectDisableForegroundColor,
  required Color expectEnableForegroundColor,
  required Color expectDisableBackgroundColor,
  required Color expectEnableBackgroundColor,
}) {
  expect(
    style.foregroundColor?.resolve(<WidgetState>{WidgetState.disabled}),
    expectDisableForegroundColor,
  );
  expect(style.foregroundColor?.resolve(<WidgetState>{}), expectEnableForegroundColor);

  expect(
    style.backgroundColor?.resolve(<WidgetState>{WidgetState.disabled}),
    expectDisableBackgroundColor,
  );
  expect(style.backgroundColor?.resolve(<WidgetState>{}), expectEnableBackgroundColor);
}

void _expectElevation(ButtonStyle style) {
  expect(
    style.elevation,
    isA<WidgetStatePropertyAll<double?>>().having(
      (WidgetStatePropertyAll<double?> p0) => p0.value,
      'check elevation',
      0,
    ),
  );
}

void _expectPadding(ButtonStyle style, EdgeInsets expectPadding) {
  expect(
    style.padding,
    isA<WidgetStatePropertyAll<EdgeInsetsGeometry?>>().having(
      (WidgetStatePropertyAll<EdgeInsetsGeometry?> p0) => p0.value,
      'check padding',
      expectPadding,
    ),
  );
}

void _expectTextStyle(
  ButtonStyle style,
  TextStyle expectDisableTextStyle,
  TextStyle expectEnableTextStyle,
) {
  expect(
    style.textStyle?.resolve(<WidgetState>{WidgetState.disabled}),
    expectDisableTextStyle,
  );

  expect(
    style.textStyle?.resolve(<WidgetState>{}),
    expectEnableTextStyle,
  );
}

void main() {
  late CommonTextStyles mockCommonTextStyles;
  late CommonColors mockCommonColors;
  late CommonButtonDimensions mockCommonButtonDimensions;
  late CommonButtonStyles commonButtonStyles;

  const double expectCornerRadius = 12;
  const EdgeInsets expectPadding = EdgeInsets.all(12);

  setUpAll(() {
    /// Setup getIt
    getIt.registerLazySingleton<CommonTextStyles>(() => MockCommonTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => MockCommonColors());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => MockCommonButtonDimensions());

    /// Setup fallback values
    registerFallbackValue(ButtonSize.large);

    mockCommonTextStyles = getIt.get<CommonTextStyles>();
    mockCommonColors = getIt.get<CommonColors>();
    mockCommonButtonDimensions = getIt.get<CommonButtonDimensions>();
    commonButtonStyles = CommonButtonStyles();

    when(() => mockCommonButtonDimensions.getCornerRadius(any())).thenReturn(expectCornerRadius);
    when(() => mockCommonButtonDimensions.getPadding(any())).thenReturn(expectPadding);
  });

  group(
    'Test function primary',
    () {
      const MaterialColor expectPrimaryButtonForegroundDisable = Colors.red;
      const MaterialColor expectPrimaryButtonForeground = Colors.green;
      const MaterialColor expectPrimaryButtonBgDisable = Colors.blue;
      const MaterialColor expectPrimaryButtonBg = Colors.yellow;

      const TextStyle expectDisableTextStyle = TextStyle(color: Colors.red, fontSize: 12);
      const TextStyle expectEnableTextStyle = TextStyle(color: Colors.green, fontSize: 12);

      setUpAll(() {
        when(() => mockCommonColors.primaryButtonForegroundDisable).thenReturn(Colors.red);
        when(() => mockCommonColors.primaryButtonForeground).thenReturn(Colors.green);
        when(() => mockCommonColors.primaryButtonBgDisable).thenReturn(Colors.blue);
        when(() => mockCommonColors.primaryButtonBg).thenReturn(Colors.yellow);

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.red,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(const TextStyle(color: Colors.red, fontSize: 12));

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.green,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(const TextStyle(color: Colors.green, fontSize: 12));
      });

      test('Test call function with tapTargetSize = MaterialTapTargetSize.padded', () {
        final ButtonStyle style = commonButtonStyles.primary(ButtonSize.large);

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectPrimaryButtonForegroundDisable,
          expectEnableForegroundColor: expectPrimaryButtonForeground,
          expectDisableBackgroundColor: expectPrimaryButtonBgDisable,
          expectEnableBackgroundColor: expectPrimaryButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.padded);
      });

      test('Test call function with tapTargetSize = MaterialTapTargetSize.shrinkWrap', () {
        final ButtonStyle style = commonButtonStyles.primary(
          ButtonSize.large,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        );

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectPrimaryButtonForegroundDisable,
          expectEnableForegroundColor: expectPrimaryButtonForeground,
          expectDisableBackgroundColor: expectPrimaryButtonBgDisable,
          expectEnableBackgroundColor: expectPrimaryButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.shrinkWrap);
      });
    },
  );

  group(
    'Test function secondary',
    () {
      final Color expectSecondaryButtonForegroundDisable = Colors.red.shade50;
      final Color expectSecondaryButtonForeground = Colors.green.shade50;
      final Color expectSecondaryButtonBgDisable = Colors.blue.shade50;
      final Color expectSecondaryButtonBg = Colors.yellow.shade50;

      final TextStyle expectDisableTextStyle = TextStyle(color: Colors.red.shade50, fontSize: 12);
      final TextStyle expectEnableTextStyle = TextStyle(color: Colors.green.shade50, fontSize: 12);

      setUpAll(() {
        when(() => mockCommonColors.secondaryButtonForegroundDisable)
            .thenReturn(Colors.red.shade50);
        when(() => mockCommonColors.secondaryButtonForeground).thenReturn(Colors.green.shade50);
        when(() => mockCommonColors.secondaryButtonBgDisable).thenReturn(Colors.blue.shade50);
        when(() => mockCommonColors.secondaryButtonBg).thenReturn(Colors.yellow.shade50);

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.red.shade50,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(TextStyle(color: Colors.red.shade50, fontSize: 12));

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.green.shade50,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(TextStyle(color: Colors.green.shade50, fontSize: 12));
      });

      test('Test call function with tapTargetSize = MaterialTapTargetSize.padded', () {
        final ButtonStyle style = commonButtonStyles.secondary(ButtonSize.large);

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectSecondaryButtonForegroundDisable,
          expectEnableForegroundColor: expectSecondaryButtonForeground,
          expectDisableBackgroundColor: expectSecondaryButtonBgDisable,
          expectEnableBackgroundColor: expectSecondaryButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.padded);
      });

      test('Test call function with tapTargetSize = MaterialTapTargetSize.shrinkWrap', () {
        final ButtonStyle style = commonButtonStyles.secondary(
          ButtonSize.large,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        );

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectSecondaryButtonForegroundDisable,
          expectEnableForegroundColor: expectSecondaryButtonForeground,
          expectDisableBackgroundColor: expectSecondaryButtonBgDisable,
          expectEnableBackgroundColor: expectSecondaryButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.shrinkWrap);
      });
    },
  );

  group(
    'Test function tertiary',
    () {
      final Color expectTertiaryButtonForegroundDisable = Colors.red.shade100;
      final Color expectTertiaryButtonForeground = Colors.green.shade100;
      final Color expectTertiaryButtonBgDisable = Colors.blue.shade100;
      final Color expectTertiaryButtonBg = Colors.yellow.shade100;

      final TextStyle expectDisableTextStyle = TextStyle(color: Colors.red.shade100, fontSize: 12);
      final TextStyle expectEnableTextStyle = TextStyle(color: Colors.green.shade100, fontSize: 12);

      setUpAll(() {
        when(() => mockCommonColors.tertiaryButtonForegroundDisable)
            .thenReturn(Colors.red.shade100);
        when(() => mockCommonColors.tertiaryButtonForeground).thenReturn(Colors.green.shade100);
        when(() => mockCommonColors.tertiaryButtonBgDisable).thenReturn(Colors.blue.shade100);
        when(() => mockCommonColors.tertiaryButtonBg).thenReturn(Colors.yellow.shade100);

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.red.shade100,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(TextStyle(color: Colors.red.shade100, fontSize: 12));

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.green.shade100,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(TextStyle(color: Colors.green.shade100, fontSize: 12));
      });

      test(
          'Test call function with tapTargetSize = MaterialTapTargetSize.padded, isHasShadow = true',
          () {
        final ButtonStyle style = commonButtonStyles.tertiary(ButtonSize.large);

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectTertiaryButtonForegroundDisable,
          expectEnableForegroundColor: expectTertiaryButtonForeground,
          expectDisableBackgroundColor: expectTertiaryButtonBgDisable,
          expectEnableBackgroundColor: expectTertiaryButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.shadowColor?.resolve(<WidgetState>{WidgetState.disabled}), isNull);
        expect(style.shadowColor?.resolve(<WidgetState>{}), isNull);

        expect(style.tapTargetSize, MaterialTapTargetSize.padded);
      });

      test(
          'Test call function with tapTargetSize = MaterialTapTargetSize.padded, isHasShadow = false',
          () {
        final ButtonStyle style = commonButtonStyles.tertiary(ButtonSize.large, isHasShadow: false);

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectTertiaryButtonForegroundDisable,
          expectEnableForegroundColor: expectTertiaryButtonForeground,
          expectDisableBackgroundColor: expectTertiaryButtonBgDisable,
          expectEnableBackgroundColor: expectTertiaryButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(
          style.shadowColor?.resolve(<WidgetState>{WidgetState.disabled}),
          Colors.yellow.shade100,
        );
        expect(style.shadowColor?.resolve(<WidgetState>{}), Colors.yellow.shade100);

        expect(style.tapTargetSize, MaterialTapTargetSize.padded);
      });

      test(
          'Test call function with tapTargetSize = MaterialTapTargetSize.shrinkWrap, isHasShadow = true',
          () {
        final ButtonStyle style = commonButtonStyles.tertiary(
          ButtonSize.large,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        );

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectTertiaryButtonForegroundDisable,
          expectEnableForegroundColor: expectTertiaryButtonForeground,
          expectDisableBackgroundColor: expectTertiaryButtonBgDisable,
          expectEnableBackgroundColor: expectTertiaryButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.shrinkWrap);
      });

      test(
          'Test call function with tapTargetSize = MaterialTapTargetSize.shrinkWrap, isHasShadow = false',
          () {
        final ButtonStyle style = commonButtonStyles.tertiary(
          ButtonSize.large,
          isHasShadow: false,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        );

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectTertiaryButtonForegroundDisable,
          expectEnableForegroundColor: expectTertiaryButtonForeground,
          expectDisableBackgroundColor: expectTertiaryButtonBgDisable,
          expectEnableBackgroundColor: expectTertiaryButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(
          style.shadowColor?.resolve(<WidgetState>{WidgetState.disabled}),
          Colors.yellow.shade100,
        );
        expect(style.shadowColor?.resolve(<WidgetState>{}), Colors.yellow.shade100);

        expect(style.tapTargetSize, MaterialTapTargetSize.shrinkWrap);
      });
    },
  );

  group(
    'Test function accent',
    () {
      final Color expectAccentButtonForegroundDisable = Colors.red.shade200;
      final Color expectAccentButtonForeground = Colors.green.shade200;
      final Color expectAccentButtonBgDisable = Colors.blue.shade200;
      final Color expectAccentButtonBg = Colors.yellow.shade200;

      final TextStyle expectDisableTextStyle = TextStyle(color: Colors.red.shade200, fontSize: 12);
      final TextStyle expectEnableTextStyle = TextStyle(color: Colors.green.shade200, fontSize: 12);

      setUpAll(() {
        when(() => mockCommonColors.accentButtonForegroundDisable).thenReturn(Colors.red.shade200);
        when(() => mockCommonColors.accentButtonForeground).thenReturn(Colors.green.shade200);
        when(() => mockCommonColors.accentButtonBgDisable).thenReturn(Colors.blue.shade200);
        when(() => mockCommonColors.accentButtonBg).thenReturn(Colors.yellow.shade200);

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.red.shade200,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(TextStyle(color: Colors.red.shade200, fontSize: 12));

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.green.shade200,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(TextStyle(color: Colors.green.shade200, fontSize: 12));
      });

      test('Test call function with tapTargetSize = MaterialTapTargetSize.padded', () {
        final ButtonStyle style = commonButtonStyles.accent(ButtonSize.large);

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectAccentButtonForegroundDisable,
          expectEnableForegroundColor: expectAccentButtonForeground,
          expectDisableBackgroundColor: expectAccentButtonBgDisable,
          expectEnableBackgroundColor: expectAccentButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.padded);
      });

      test('Test call function with tapTargetSize = MaterialTapTargetSize.shrinkWrap', () {
        final ButtonStyle style = commonButtonStyles.accent(
          ButtonSize.large,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        );

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectAccentButtonForegroundDisable,
          expectEnableForegroundColor: expectAccentButtonForeground,
          expectDisableBackgroundColor: expectAccentButtonBgDisable,
          expectEnableBackgroundColor: expectAccentButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.shrinkWrap);
      });
    },
  );

  group(
    'Test function negative',
    () {
      final Color expectNegativeButtonForegroundDisable = Colors.red.shade200;
      final Color expectNegativeButtonForeground = Colors.green.shade200;
      final Color expectNegativeButtonBgDisable = Colors.blue.shade200;
      final Color expectNegativeButtonBg = Colors.yellow.shade200;

      final TextStyle expectDisableTextStyle = TextStyle(color: Colors.red.shade200, fontSize: 12);
      final TextStyle expectEnableTextStyle = TextStyle(color: Colors.green.shade200, fontSize: 12);

      setUpAll(() {
        when(() => mockCommonColors.negativeButtonForegroundDisable)
            .thenReturn(Colors.red.shade200);
        when(() => mockCommonColors.negativeButtonForeground).thenReturn(Colors.green.shade200);
        when(() => mockCommonColors.negativeButtonBgDisable).thenReturn(Colors.blue.shade200);
        when(() => mockCommonColors.negativeButtonBg).thenReturn(Colors.yellow.shade200);

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.red.shade200,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(TextStyle(color: Colors.red.shade200, fontSize: 12));

        when(
          () => mockCommonTextStyles.button(
            any(),
            Colors.green.shade200,
            fontSize: any(named: 'fontSize'),
          ),
        ).thenReturn(TextStyle(color: Colors.green.shade200, fontSize: 12));
      });

      test('Test call function with tapTargetSize = MaterialTapTargetSize.padded', () {
        final ButtonStyle style = commonButtonStyles.negative(ButtonSize.large);

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectNegativeButtonForegroundDisable,
          expectEnableForegroundColor: expectNegativeButtonForeground,
          expectDisableBackgroundColor: expectNegativeButtonBgDisable,
          expectEnableBackgroundColor: expectNegativeButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.padded);
      });

      test('Test call function with tapTargetSize = MaterialTapTargetSize.shrinkWrap', () {
        final ButtonStyle style = commonButtonStyles.negative(
          ButtonSize.large,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        );

        _expectElevation(style);

        _expectColors(
          style,
          expectDisableForegroundColor: expectNegativeButtonForegroundDisable,
          expectEnableForegroundColor: expectNegativeButtonForeground,
          expectDisableBackgroundColor: expectNegativeButtonBgDisable,
          expectEnableBackgroundColor: expectNegativeButtonBg,
        );

        _expectPadding(style, expectPadding);

        _expectTextStyle(style, expectDisableTextStyle, expectEnableTextStyle);

        expect(style.tapTargetSize, MaterialTapTargetSize.shrinkWrap);
      });
    },
  );
}
