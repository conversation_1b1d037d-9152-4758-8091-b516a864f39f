import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const int fakeStatusCode = 500;
  const String fakeUserMessage = 'user_message_value';
  const String fakeUserMessageTitle = 'user_message_title_value';
  const String fakeVerdict = 'fake_verdict';

  test('Initialization', () {
    final ErrorUIModel errorModel = ErrorUIModel(
      statusCode: fakeStatusCode,
      verdict: fakeVerdict,
      userMessage: fakeUserMessage,
      userMessageTitle: fakeUserMessageTitle,
    );
    expect(errorModel.statusCode, fakeStatusCode);
    expect(errorModel.verdict, fakeVerdict);
    expect(errorModel.userMessage, fakeUserMessage);
    expect(errorModel.userMessageTitle, fakeUserMessageTitle);
  });

  test('Initialization from BaseEntity', () {
    final Map<String, dynamic> response = <String, dynamic>{
      'data': <String, dynamic>{
        'user_message': fakeUserMessage,
        'user_message_title': fakeUserMessageTitle,
      },
      'verdict': fakeVerdict,
    };

    final BaseResponse baseResponse = BaseResponse(
      statusCode: fakeStatusCode,
      response: response,
    );

    final BaseEntity baseEntity = BaseEntity.fromBaseResponse(baseResponse);

    final ErrorUIModel errorModel = ErrorUIModel.fromEntity(baseEntity);
    expect(errorModel.statusCode, fakeStatusCode);
    expect(errorModel.verdict, fakeVerdict);
    expect(errorModel.userMessage, fakeUserMessage);
    expect(errorModel.userMessageTitle, fakeUserMessageTitle);
  });

  test('Initialization from null BaseEntity', () {
    final ErrorUIModel errorModel = ErrorUIModel.fromEntity(null);
    expect(errorModel.statusCode, isNull);
    expect(errorModel.verdict, isNull);
    expect(errorModel.userMessage, isNull);
    expect(errorModel.userMessageTitle, isNull);
  });
}
