// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_registration_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

import 'module_test_utils.dart';

// Mock classes
class MockFeatureModule extends Mock implements FeatureModule {}

void main() {
  group('ModuleRegistrationModel', () {
    late FeatureModule mockModule;
    late DateTime testTime;
    const String testSource = 'test_source';

    setUp(() {
      mockModule = MockFeatureModule();
      testTime = DateTime(2024, 1, 1, 12, 0, 0);
      when(() => mockModule.name).thenReturn('test_module');
      when(() => mockModule.dependencies).thenReturn([]);
    });

    group('Constructor', () {
      test('should create with all required parameters', () {
        // Act
        final registration = ModuleRegistrationModel(
          module: mockModule,
          source: testSource,
          registeredAt: testTime,
        );

        // Assert
        expect(registration.module, equals(mockModule));
        expect(registration.source, equals(testSource));
        expect(registration.registeredAt, equals(testTime));
      });

      test('should create with real module instance', () {
        // Arrange
        final realModule = ModuleTestUtils.createTestModule(
          name: 'real_module',
          dependencies: [String, int],
        );

        // Act
        final registration = ModuleRegistrationModel(
          module: realModule,
          source: 'common_package',
          registeredAt: testTime,
        );

        // Assert
        expect(registration.module, equals(realModule));
        expect(registration.source, equals('common_package'));
        expect(registration.registeredAt, equals(testTime));
      });
    });

    group('Properties', () {
      test('should have immutable properties', () {
        // Arrange
        final registration = ModuleRegistrationModel(
          module: mockModule,
          source: testSource,
          registeredAt: testTime,
        );

        // Act & Assert - Properties should be final and not changeable
        expect(registration.module, same(mockModule));
        expect(registration.source, same(testSource));
        expect(registration.registeredAt, same(testTime));
      });
    });

    group('Edge Cases', () {
      test('should handle empty source string', () {
        // Act
        final registration = ModuleRegistrationModel(
          module: mockModule,
          source: '',
          registeredAt: testTime,
        );

        // Assert
        expect(registration.source, equals(''));
      });
    });

    group('toString Method', () {
      test('should return meaningful string representation', () {
        // Arrange
        when(() => mockModule.name).thenReturn('test_module');
        final registration = ModuleRegistrationModel(
          module: mockModule,
          source: testSource,
          registeredAt: testTime,
        );

        // Act
        final result = registration.toString();

        // Assert
        expect(result, isA<String>());
        expect(result, contains('ModuleRegistration'));
        expect(result, contains('test_module'));
        expect(result, contains(testSource));
        expect(result, contains(testTime.toString()));
      });
    });

  });
}
