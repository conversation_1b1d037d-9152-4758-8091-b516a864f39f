// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:dio/dio.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/analytics_service_impl.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

/// Mock implementation of FeatureModule for testing
class MockFeatureModule extends Mock implements FeatureModule {}

/// Mock implementation of AnalyticsModule for testing
class MockAnalyticsModule extends MockFeatureModule {
  MockAnalyticsModule() {
    // Configure default behavior
    when(() => name).thenReturn(CommonPackageModuleNames.analytics);
    when(() => dependencies).thenReturn(<Type>[
      LoggingRepo,
      EventTrackingUtils,
      FirebaseAnalyticsWrapper,
      CommonNavigatorObserver,
      DatadogLogger,
      AnalyticsServiceImpl,
    ]);
    when(() => register(any())).thenAnswer((_) async {
      // Default implementation does nothing
    });
  }

  /// Configure this mock to register mock implementations of all dependencies
  void setupWithMockDependencies() {
    when(() => register(any())).thenAnswer((Invocation invocation) async {
      final GetIt getIt = invocation.positionalArguments[0] as GetIt;

      // Register mock EventTrackingUtils if not already registered
      if (!getIt.isRegistered<EventTrackingUtils>()) {
        final MockEventTrackingUtils mockEventTrackingUtils = MockEventTrackingUtils();
        getIt.registerLazySingleton<EventTrackingUtils>(() => mockEventTrackingUtils);
      }

      // Register mock FirebaseAnalyticsWrapper if not already registered
      if (!getIt.isRegistered<FirebaseAnalyticsWrapper>()) {
        final MockFirebaseAnalyticsWrapper mockFirebaseAnalytics = MockFirebaseAnalyticsWrapper();
        getIt.registerLazySingleton<FirebaseAnalyticsWrapper>(() => mockFirebaseAnalytics);
      }

      // Register CommonNavigatorObserver if not already registered
      if (!getIt.isRegistered<CommonNavigatorObserver>()) {
        getIt.registerLazySingleton<CommonNavigatorObserver>(() => CommonNavigatorObserver());
      }

      // Register mock AnalyticsServiceImpl if not already registered
      if (!getIt.isRegistered<AnalyticsServiceImpl>()) {
        final MockAnalyticsServiceImpl mockAnalyticsService = MockAnalyticsServiceImpl();
        getIt.registerLazySingleton<AnalyticsServiceImpl>(() => mockAnalyticsService);
      }
    });
  }
}

/// Mock implementation of NetworkModule for testing
class MockNetworkModule extends MockFeatureModule {
  MockNetworkModule() {
    // Configure default behavior
    when(() => name).thenReturn(CommonPackageModuleNames.network);
    when(() => dependencies).thenReturn(<Type>[
      Connectivity,
      NetworkManager,
      Dio,
      CommonHttpClient,
    ]);
    when(() => register(any())).thenAnswer((_) async {
      // Default implementation does nothing
    });
  }

  /// Configure this mock to register mock implementations of all dependencies
  void setupWithMockDependencies() {
    when(() => register(any())).thenAnswer((Invocation invocation) async {
      final GetIt getIt = invocation.positionalArguments[0] as GetIt;

      // Register mock Connectivity if not already registered
      if (!getIt.isRegistered<Connectivity>()) {
        final MockConnectivity mockConnectivity = MockConnectivity();
        when(() => mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => <ConnectivityResult>[ConnectivityResult.wifi]);
        when(() => mockConnectivity.onConnectivityChanged)
            .thenAnswer((_) => Stream.value(<ConnectivityResult>[ConnectivityResult.wifi]));
        getIt.registerLazySingleton<Connectivity>(() => mockConnectivity);
      }

      // Register mock NetworkManager if not already registered
      if (!getIt.isRegistered<NetworkManager>()) {
        final MockNetworkManager mockNetworkManager = MockNetworkManager();
        when(() => mockNetworkManager.hasInternet).thenReturn(true);
        when(() => mockNetworkManager.myStreamNetwork).thenAnswer((_) => Stream.value(true));
        when(() => mockNetworkManager.checkOnlineNetwork()).thenAnswer((_) async => true);
        when(() => mockNetworkManager.initialise()).thenAnswer((_) async {});
        getIt.registerLazySingleton<NetworkManager>(() => mockNetworkManager);
      }

      // Register Dio if not already registered
      if (!getIt.isRegistered<Dio>()) {
        final Dio dio = Dio();
        getIt.registerLazySingleton<Dio>(() => dio);
      }

      // Register mock CommonHttpClient if not already registered
      if (!getIt.isRegistered<CommonHttpClient>()) {
        final MockCommonHttpClient mockHttpClient = MockCommonHttpClient();
        getIt.registerLazySingleton<CommonHttpClient>(() => mockHttpClient);
      }
    });
  }
}

// Mock implementations of dependencies

/// Mock implementation of EventTrackingUtils
class MockEventTrackingUtils extends Mock implements EventTrackingUtils {
  final List<Map<String, dynamic>> _events = <Map<String, dynamic>>[];
  final List<Map<String, dynamic>> _errors = <Map<String, dynamic>>[];

  /// Gets the list of tracked events
  List<Map<String, dynamic>> get events => _events;

  /// Gets the list of tracked errors
  List<Map<String, dynamic>> get errors => _errors;

  MockEventTrackingUtils() {
    // Configure default behavior
    when(() => sendUserActionEvent(eventId: any(named: 'eventId'), metaData: any(named: 'metaData')))
        .thenAnswer((Invocation invocation) async {
      final String eventId = invocation.namedArguments[#eventId] as String;
      final Map<String, dynamic>? metaData = invocation.namedArguments[#metaData] as Map<String, dynamic>?;

      _events.add(<String, dynamic>{
        'eventName': eventId,
        'parameters': metaData ?? <String, dynamic>{},
      });
    });
  }
}

/// Mock implementation of FirebaseAnalyticsWrapper
class MockFirebaseAnalyticsWrapper extends Mock implements FirebaseAnalyticsWrapper {
  final List<Map<String, dynamic>> _events = <Map<String, dynamic>>[];
  final List<Map<String, dynamic>> _screenViews = <Map<String, dynamic>>[];

  /// Gets the list of tracked events
  List<Map<String, dynamic>> get events => _events;

  /// Gets the list of tracked screen views
  List<Map<String, dynamic>> get screenViews => _screenViews;

  MockFirebaseAnalyticsWrapper() {
    // Configure default behavior
    when(() => logEvent(name: any(named: 'name'), parameters: any(named: 'parameters'))).thenAnswer((Invocation invocation) async {
      final String name = invocation.namedArguments[#name] as String;
      final Map<String, Object>? parameters = invocation.namedArguments[#parameters] as Map<String, Object>?;

      _events.add(<String, dynamic>{
        'name': name,
        'parameters': parameters ?? <String, Object>{},
      });
    });
  }
}

/// Mock implementation of Connectivity
class MockConnectivity extends Mock implements Connectivity {}

/// Mock implementation of NetworkManager
class MockNetworkManager extends Mock implements NetworkManager {}

/// Mock implementation of CommonHttpClient
class MockCommonHttpClient extends Mock implements CommonHttpClient {}

/// Mock implementation of DevicePlatform
class MockDevicePlatform extends Mock implements DevicePlatform {}

/// Mock implementation of LoggingRepo
class MockLoggingRepo extends Mock implements LoggingRepo {}

/// Mock implementation of AnalyticsServiceImpl
class MockAnalyticsServiceImpl extends Mock implements AnalyticsServiceImpl {}
