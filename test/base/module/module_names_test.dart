// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CommonPackageModuleNames', () {
    test('should have unique module names', () {
      // Get all module names from the class
      final List<String> moduleNames = [
        CommonPackageModuleNames.core,
        CommonPackageModuleNames.network,
        CommonPackageModuleNames.analytics,
        CommonPackageModuleNames.deviceInfo,
        CommonPackageModuleNames.dataCollection,
        CommonPackageModuleNames.notification,
        CommonPackageModuleNames.ui,
        CommonPackageModuleNames.utility,
        CommonPackageModuleNames.ekyc,
      ];

      // Create a set from the list to check for duplicates
      final Set<String> uniqueModuleNames = Set<String>.from(moduleNames);

      // If there are duplicates, the set size will be smaller than the list size
      expect(uniqueModuleNames.length, equals(moduleNames.length),
          reason: 'Module names should be unique');
    });

    test('should have non-empty module names', () {
      // Get all module names from the class
      final List<String> moduleNames = [
        CommonPackageModuleNames.core,
        CommonPackageModuleNames.network,
        CommonPackageModuleNames.analytics,
        CommonPackageModuleNames.deviceInfo,
        CommonPackageModuleNames.dataCollection,
        CommonPackageModuleNames.notification,
        CommonPackageModuleNames.ui,
        CommonPackageModuleNames.utility,
        CommonPackageModuleNames.ekyc,
      ];

      // Check that all module names are non-empty
      for (final String moduleName in moduleNames) {
        expect(moduleName.isNotEmpty, isTrue,
            reason: 'Module names should not be empty');
      }
    });

    test('should have consistent naming convention', () {
      // Get all module names from the class
      final List<String> moduleNames = [
        CommonPackageModuleNames.core,
        CommonPackageModuleNames.network,
        CommonPackageModuleNames.analytics,
        CommonPackageModuleNames.deviceInfo,
        CommonPackageModuleNames.dataCollection,
        CommonPackageModuleNames.notification,
        CommonPackageModuleNames.ui,
        CommonPackageModuleNames.utility,
        CommonPackageModuleNames.ekyc,
      ];

      // Check that all module names follow a consistent naming convention
      // In this case, we'll check that they're all valid identifiers (no spaces, special chars)
      final RegExp validNamePattern = RegExp(r'^[a-zA-Z_][a-zA-Z0-9_]*$');
      for (final String moduleName in moduleNames) {
        expect(validNamePattern.hasMatch(moduleName), isTrue,
            reason: 'Module name "$moduleName" should follow the naming convention');
      }
    });

    test('should have all module names defined as constants', () {
      // Verify that all module names are defined as constants
      expect(CommonPackageModuleNames.core, isA<String>());
      expect(CommonPackageModuleNames.network, isA<String>());
      expect(CommonPackageModuleNames.analytics, isA<String>());
      expect(CommonPackageModuleNames.deviceInfo, isA<String>());
      expect(CommonPackageModuleNames.dataCollection, isA<String>());
      expect(CommonPackageModuleNames.notification, isA<String>());
      expect(CommonPackageModuleNames.ui, isA<String>());
      expect(CommonPackageModuleNames.utility, isA<String>());
      expect(CommonPackageModuleNames.ekyc, isA<String>());
    });

    test('should have correct values for module names', () {
      // Verify the actual values of the module names
      expect(CommonPackageModuleNames.core, equals('common_core'));
      expect(CommonPackageModuleNames.network, equals('common_network'));
      expect(CommonPackageModuleNames.analytics, equals('common_analytics'));
      expect(CommonPackageModuleNames.dataCollection, equals('common_data_collection'));
      expect(CommonPackageModuleNames.notification, equals('common_notification'));
      expect(CommonPackageModuleNames.ui, equals('common_ui'));
      expect(CommonPackageModuleNames.utility, equals('common_utility'));
      expect(CommonPackageModuleNames.ekyc, equals('common_ekyc'));
      expect(CommonPackageModuleNames.deviceInfo, equals('common_device_info'));
    });

    test('should follow naming convention with common_ prefix', () {
      // Verify that all module names follow the naming convention
      expect(CommonPackageModuleNames.core, startsWith('common_'));
      expect(CommonPackageModuleNames.network, startsWith('common_'));
      expect(CommonPackageModuleNames.analytics, startsWith('common_'));
      expect(CommonPackageModuleNames.dataCollection, startsWith('common_'));
      expect(CommonPackageModuleNames.notification, startsWith('common_'));
      expect(CommonPackageModuleNames.ui, startsWith('common_'));
      expect(CommonPackageModuleNames.utility, startsWith('common_'));
      expect(CommonPackageModuleNames.ekyc, startsWith('common_'));
      expect(CommonPackageModuleNames.deviceInfo, startsWith('common_'));
    });
  });
}
