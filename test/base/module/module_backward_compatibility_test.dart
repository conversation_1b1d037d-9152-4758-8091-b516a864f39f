// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/services.dart';

import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_registry.dart';
import 'package:flutter_common_package/base/module/module_registry_builder.dart';

import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:package_info_plus/package_info_plus.dart';

class MockDeviceInfoPluginWrapper extends Mock implements DeviceInfoPluginWrapper {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

void main() {
  // Initialize the binding
  TestWidgetsFlutterBinding.ensureInitialized();

  // Mock the connectivity channel
  const MethodChannel channel = MethodChannel('dev.fluttercommunity.plus/connectivity');
  TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
    channel,
    (MethodCall methodCall) async {
      if (methodCall.method == 'check') {
        return <String>['wifi'];
      }
      return null;
    },
  );

  late GetIt getIt;

  setUp(() {
    getIt = GetIt.instance;
    getIt.reset();

    // Initialize FlavorConfig
    FlavorConfig(
      flavor: 'test',
      values: CommonFlavorValues(
        baseUrl: 'https://test.example.com',
        oneSignalAppId: 'test-id',
        initializeFirebaseSdk: false,
      ),
    );

    // Mock PackageInfo.fromPlatform
    PackageInfo.setMockInitialValues(
      appName: 'Test App',
      packageName: 'com.test.app',
      version: '1.0.0',
      buildNumber: '1',
      buildSignature: '',
    );

    // Register required mocks for the test
    final MockDeviceInfoPluginWrapper mockDeviceInfoPluginWrapper = MockDeviceInfoPluginWrapper();
    final MockDevicePlatform mockDevicePlatform = MockDevicePlatform();

    // Configure mocks
    when(() => mockDeviceInfoPluginWrapper.getDeviceModel()).thenReturn('Test Device');
    when(() => mockDeviceInfoPluginWrapper.getPlatformName()).thenReturn('test');
    when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenReturn('1.0.0');
    when(() => mockDeviceInfoPluginWrapper.getAndroidBuildNumber()).thenReturn('123');
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);

    // Register the mocks in GetIt
    getIt.registerSingleton<DeviceInfoPluginWrapper>(mockDeviceInfoPluginWrapper);
    getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);
    getIt.registerSingleton<Connectivity>(Connectivity());

  });

  group('Backward Compatibility', () {
    test('initCommonPackage should register modules', () async {
      // Create a builder directly to avoid initialization issues
      final ModuleRegistryBuilder builder = ModuleRegistry.builder(getIt);

      // Use the internal method from init_common_package.dart
      // This is a bit of a hack, but it's the only way to test the registration without initialization
      final ModuleRegistry registry = builder.build();
      moduleRegistry = registry;

      // Assert that the registry is created
      expect(moduleRegistry, isNotNull);
    });

    test('ModuleRegistry should handle module registration and validation', () {
      // Create a builder
      final ModuleRegistryBuilder builder = ModuleRegistry.builder(getIt);

      // Create a simple test module
      final _TestModule testModule = _TestModule(
        name: 'test_module',
      );

      // Register the module
      builder.register(testModule, source: 'test');

      // Build the registry
      final ModuleRegistry registry = builder.build();

      // Verify the module is registered
      expect(registry.isModuleRegistered('test_module'), isTrue);
      expect(registry.registeredModules, contains('test_module'));
    });

    test('ModuleRegistry should detect duplicate module names', () {
      // Create a builder
      final ModuleRegistryBuilder builder = ModuleRegistry.builder(getIt);

      // Create two modules with the same name but different types
      final _TestModule testModule1 = _TestModule(
        name: 'duplicate_module',
      );

      final _TestModule2 testModule2 = _TestModule2(
        name: 'duplicate_module',
      );

      // Register the first module
      builder.register(testModule1, source: 'test');

      // Registering the second module should throw an exception
      expect(
        () => builder.register(testModule2, source: 'test2'),
        throwsException,
      );
    });
  });
}

/// A simple test module implementation for testing
class _TestModule implements FeatureModule {
  final String _name;
  final List<Type> _dependencies;

  _TestModule({
    required String name,
    List<Type>? dependencies,
  }) : _name = name,
       _dependencies = dependencies ?? <Type>[String];

  @override
  String get name => _name;

  @override
  List<Type> get dependencies => _dependencies;

  @override
  Future<void> register(GetIt getIt) async {
    // No-op for testing
  }
}

/// Another test module implementation for testing duplicates
class _TestModule2 implements FeatureModule {
  final String _name;
  final List<Type> _dependencies;

  _TestModule2({
    required String name,
    List<Type>? dependencies,
  }) : _name = name,
       _dependencies = dependencies ?? <Type>[int];

  @override
  String get name => _name;

  @override
  List<Type> get dependencies => _dependencies;

  @override
  Future<void> register(GetIt getIt) async {
    // No-op for testing
  }
}
