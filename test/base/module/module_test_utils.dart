// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_common_package/base/module/common_package_modules/ui_module.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_registry.dart';

import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

import 'mocks/mock_modules.dart';

/// A utility class for setting up test mocks and modules
class ModuleTestUtils {
  /// Sets up a test environment with mocked dependencies and a module registry
  ///
  /// Returns a [ModuleRegistry] that can be used to register and initialize modules
  static ModuleRegistry setupTestEnvironment(GetIt getIt) {
    // Reset GetIt instance
    getIt.reset();

    // Create module registry builder directly
    final moduleRegistryBuilder = ModuleRegistry.builder(getIt);

    // Create mocks
    final LoggingRepo mockLoggingRepo = MockLoggingRepo();
    final Connectivity mockConnectivity = MockConnectivity();
    final NetworkManager mockNetworkManager = MockNetworkManager();
    final DevicePlatform mockDevicePlatform = MockDevicePlatform();

    // Setup mock behaviors
    when(() => mockConnectivity.checkConnectivity())
        .thenAnswer((_) async => <ConnectivityResult>[ConnectivityResult.wifi]);

    // Register mocks
    getIt.registerSingleton<LoggingRepo>(mockLoggingRepo);
    getIt.registerSingleton<Connectivity>(mockConnectivity);
    getIt.registerSingleton<NetworkManager>(mockNetworkManager);
    getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);

    return moduleRegistryBuilder.build();
  }

  /// Creates and returns a ModuleRegistry with common test modules registered
  static ModuleRegistry registerCommonTestModules() {
    // Create mock modules with configured behaviors
    final MockNetworkModule mockNetworkModule = MockNetworkModule();
    mockNetworkModule.setupWithMockDependencies();

    final MockAnalyticsModule mockAnalyticsModule = MockAnalyticsModule();
    mockAnalyticsModule.setupWithMockDependencies();

    // Get the builder from the registry directly
    final builder = ModuleRegistry.builder(GetIt.instance);

    // Register the modules
    builder.register(mockNetworkModule, source: 'test');
    builder.register(mockAnalyticsModule, source: 'test');
    builder.register(UiModule(), source: 'test');

    // Build and return the registry
    return builder.build();
  }

  /// Creates a simple test module with the given name and dependencies
  static FeatureModule createTestModule({
    required String name,
    List<Type> dependencies = const [],
    Future<void> Function(GetIt)? registerFn,
  }) {
    return _TestModule(
      name: name,
      dependencies: dependencies,
      registerFn: registerFn ?? ((getIt) async {}),
    );
  }
}

/// A simple implementation of FeatureModule for testing
class _TestModule implements FeatureModule {
  final String _name;
  final List<Type> _dependencies;
  final Future<void> Function(GetIt) _registerFn;

  _TestModule({
    required String name,
    required List<Type> dependencies,
    required Future<void> Function(GetIt) registerFn,
  })  : _name = name,
        _dependencies = dependencies,
        _registerFn = registerFn;

  @override
  String get name => _name;

  @override
  List<Type> get dependencies => _dependencies;

  @override
  Future<void> register(GetIt getIt) => _registerFn(getIt);
}
