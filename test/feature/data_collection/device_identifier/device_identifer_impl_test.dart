import 'package:flutter_common_package/feature/data_collection/device_identifier/android_device_identifier/android_device_identifier.dart';
import 'package:flutter_common_package/feature/data_collection/device_identifier/device_identifer_impl.dart';
import 'package:flutter_common_package/feature/data_collection/device_identifier/device_identifier.dart';
import 'package:flutter_common_package/feature/data_collection/device_identifier/ios_device_identifier/ios_device_identifier.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockAndroidDeviceIdentifier extends Mock implements AndroidDeviceIdentifier {}

class MockIosDeviceIdentifier extends Mock implements IosDeviceIdentifier {}

void main() {
  final DevicePlatform mockDevicePlatform = MockDevicePlatform();
  final AndroidDeviceIdentifier mockAndroidDeviceIdentifier = MockAndroidDeviceIdentifier();
  final IosDeviceIdentifier mockIosDeviceIdentifier = MockIosDeviceIdentifier();

  final DeviceIdentifier deviceIdentifier = DeviceIdentifierImpl(
    devicePlatform: mockDevicePlatform,
    androidDeviceIdentifier: mockAndroidDeviceIdentifier,
    iosDeviceIdentifier: mockIosDeviceIdentifier,
  );

  test('getDeviceId should return the Android ID when running on Android', () async {
    const String expectDeviceId = 'android_id';
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);
    when(() => mockAndroidDeviceIdentifier.getAndroidId()).thenAnswer((_) async => expectDeviceId);

    final String? result = await deviceIdentifier.getDeviceId();
    expect(result, expectDeviceId);
    verify(() => mockAndroidDeviceIdentifier.getAndroidId()).called(1);
    verifyNever(() => mockIosDeviceIdentifier.getIosId());
  });

  test('getDeviceId should return the iOS ID when running on iOS', () async {
    const String expectDeviceId = 'ios_id';
    when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
    when(() => mockDevicePlatform.isIOS()).thenReturn(true);
    when(() => mockIosDeviceIdentifier.getIosId()).thenAnswer((_) async => expectDeviceId);

    final String? result = await deviceIdentifier.getDeviceId();
    expect(result, expectDeviceId);
    verifyNever(() => mockAndroidDeviceIdentifier.getAndroidId());
    verify(() => mockIosDeviceIdentifier.getIosId()).called(1);
  });

  test('getDeviceId should return null when the platform is not android OR iOS', () async {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);

    final String? result = await deviceIdentifier.getDeviceId();
    expect(result, isNull);
    verifyNever(() => mockAndroidDeviceIdentifier.getAndroidId());
    verifyNever(() => mockIosDeviceIdentifier.getIosId());
  });
}
