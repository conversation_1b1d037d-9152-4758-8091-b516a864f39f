import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_common_package/feature/data_collection/firebase_performance/fp_app_start_metric_recorder.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFirebasePerformance extends Mo<PERSON> implements FirebasePerformance {}

class MockTrace extends Mock implements Trace {}

class MockStopwatch extends Mock implements Stopwatch {}

void main() {
  late FPAppStartMetricRecorder recorder;
  late MockFirebasePerformance mockFirebasePerformance;
  late MockTrace mockTrace;
  late MockStopwatch mockStopwatch;

  setUp(() {
    recorder = FPAppStartMetricRecorder();
    mockFirebasePerformance = MockFirebasePerformance();
    mockTrace = MockTrace();
    mockStopwatch = MockStopwatch();

    when(() => mockFirebasePerformance.newTrace('flutter_app_start')).thenReturn(mockTrace);
    when(() => mockTrace.start()).thenAnswer((_) => Future<void>.value());
    when(() => mockTrace.stop()).thenAnswer((_) => Future<void>.value());
    when(() => mockTrace.setMetric(any(), any())).thenAnswer((_) {});
    when(() => mockStopwatch.start()).thenAnswer((_) {});
    when(() => mockStopwatch.stop()).thenAnswer((_) {});
  });

  void resetRecorder() {
    recorder.trace = mockTrace;
    recorder.stopwatch = mockStopwatch;
    recorder.previousElapsedTime = 0;
  }

  group('FPAppStartMetricRecorder', () {
    test('singleton instance should work correctly', () {
      final FPAppStartMetricRecorder instance1 = FPAppStartMetricRecorder();
      final FPAppStartMetricRecorder instance2 = FPAppStartMetricRecorder();

      expect(instance1, same(instance2));
    });

    test('startAppStartUpRecord should initialize trace and stopwatch', () {
      recorder.startAppStartUpRecord(mockFirebasePerformance);

      expect(recorder.trace, isNotNull);
      expect(recorder.stopwatch, isNotNull);
      verify(() => mockFirebasePerformance.newTrace('flutter_app_start')).called(1);
      verify(() => mockTrace.start()).called(1);
    });

    test('stopAppStartUpRecord should stop trace and stopwatch', () {
      resetRecorder();
      recorder.stopAppStartUpRecord();

      verify(() => mockTrace.stop()).called(1);
      verify(() => mockStopwatch.stop()).called(1);
    });

    test('recordMetric should calculate and set elapsed time correctly', () {
      resetRecorder();

      // Initial time is 100ms
      when(() => mockStopwatch.elapsedMilliseconds).thenReturn(100);
      recorder.recordMetric('first_metric');
      verify(() => mockTrace.setMetric('first_metric', 100)).called(1);

      // Increment to 250ms (150ms difference)
      when(() => mockStopwatch.elapsedMilliseconds).thenReturn(250);
      recorder.recordMetric('second_metric');
      verify(() => mockTrace.setMetric('second_metric', 150)).called(1);

      // Increment to 400ms (150ms difference)
      when(() => mockStopwatch.elapsedMilliseconds).thenReturn(400);
      recorder.recordMetric('third_metric');
      verify(() => mockTrace.setMetric('third_metric', 150)).called(1);
    });

    test('complete workflow test with actual firebase performance', () {
      recorder.startAppStartUpRecord(mockFirebasePerformance);
      resetRecorder();

      when(() => mockStopwatch.elapsedMilliseconds).thenAnswer((_) => 50);
      recorder.recordMetric('splash_screen');
      verify(() => mockTrace.setMetric('splash_screen', 50)).called(1);

      when(() => mockStopwatch.elapsedMilliseconds).thenAnswer((_) => 250);
      recorder.recordMetric('home_screen');
      verify(() => mockTrace.setMetric('home_screen', 200)).called(1);

      recorder.stopAppStartUpRecord();
      verify(() => mockTrace.stop()).called(1);
    });
  });
}
