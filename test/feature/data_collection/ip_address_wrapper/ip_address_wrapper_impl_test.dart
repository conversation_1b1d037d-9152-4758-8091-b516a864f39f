import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/data_collection/ip_address_wrapper/ip_address_wrapper_impl.dart';
import 'package:flutter_common_package/feature/data_collection/model/ip_address_info.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:network_info_plus/network_info_plus.dart';

class MockNetworkInfo extends Mock implements NetworkInfo {}

class MockConnectivity extends Mock implements Connectivity {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  final NetworkInfo mockNetworkInfo = MockNetworkInfo();
  final Connectivity mockConnectivity = MockConnectivity();
  late LoggingRepo mockLoggingRepo;
  const String expectLocalIpAddress = '***********';

  final IpAddressWrapperImpl ipAddressWrapperImpl = IpAddressWrapperImpl(
    networkInfo: mockNetworkInfo,
    connectivity: mockConnectivity,
  );

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('Test getIpAddressInfo', () {
    test('Test case: the connection is wifi', () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) async => [ConnectivityResult.wifi],
      );
      when(() => mockNetworkInfo.getWifiIP()).thenAnswer((_) async => '***********');
      final IpAddressInfo? result = await ipAddressWrapperImpl.getIpAddressInfo();
      expect(
        result,
        isA<IpAddressInfo>().having(
          (IpAddressInfo p0) => p0.localDeviceIpAddress,
          'test local ip address',
          expectLocalIpAddress,
        ),
      );
      verify(() => mockConnectivity.checkConnectivity()).called(1);
      verify(() => mockNetworkInfo.getWifiIP()).called(1);
    });

    test('Test case: the connection is non-wifi', () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) async => [ConnectivityResult.mobile],
      );
      final IpAddressInfo? result = await ipAddressWrapperImpl.getIpAddressInfo();
      expect(
        result,
        isA<IpAddressInfo>().having(
          (IpAddressInfo p0) => p0.localDeviceIpAddress,
          'test local ip address',
          isNull,
        ),
      );
      verify(() => mockConnectivity.checkConnectivity()).called(1);
      verifyNever(() => mockNetworkInfo.getWifiIP());
    });

    test('Test case: checkConnectivity throw exception', () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) async => throw Exception('checkConnectivity exception'),
      );
      when(() => mockLoggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) async => Future<void>.value());

      final IpAddressInfo? result = await ipAddressWrapperImpl.getIpAddressInfo();
      expect(result, isNull);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
      verifyNever(() => mockNetworkInfo.getWifiIP());
      expect(
          verify(() => mockLoggingRepo.logErrorEvent(
                errorType: captureAny(named: 'errorType'),
                args: captureAny(named: 'args'),
              )).captured,
          <dynamic>[
            'ip_address_wrapper',
            <String, dynamic>{
              'action': 'get_ip_address_info',
              'description': 'Exception: checkConnectivity exception',
            }
          ]);
    });

    test('Test case: getWifiIP throw exception', () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) async => [ConnectivityResult.wifi],
      );
      when(() => mockNetworkInfo.getWifiIP()).thenAnswer(
        (_) async => throw Exception('getWifiIP exception'),
      );
      when(() => mockLoggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) async => Future<void>.value());

      final IpAddressInfo? result = await ipAddressWrapperImpl.getIpAddressInfo();
      expect(result, isNull);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
      verify(() => mockNetworkInfo.getWifiIP()).called(1);
      expect(
          verify(() => mockLoggingRepo.logErrorEvent(
                errorType: captureAny(named: 'errorType'),
                args: captureAny(named: 'args'),
              )).captured,
          <dynamic>[
            'ip_address_wrapper',
            <String, dynamic>{
              'action': 'get_ip_address_info',
              'description': 'Exception: getWifiIP exception',
            }
          ]);
    });
  });
}
