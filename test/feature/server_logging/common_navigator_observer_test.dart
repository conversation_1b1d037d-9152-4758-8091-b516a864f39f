import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockPopupRoute extends PopupRoute<dynamic> {
  @override
  Color get barrierColor => Colors.black54;

  @override
  bool get barrierDismissible => false;

  @override
  String? get barrierLabel => '';

  @override
  Widget buildPage(
      BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
    return const SizedBox.shrink();
  }

  @override
  Duration get transitionDuration => const Duration(milliseconds: 250);
}

class MockPageRoute extends PageRoute<dynamic> {
  @override
  Color? get barrierColor => Colors.black54;

  @override
  String? get barrierLabel => '';

  @override
  Widget buildPage(
      BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
    return const SizedBox.shrink();
  }

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => const Duration(milliseconds: 250);
}

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  late CommonNavigatorObserver observer;
  final MockPopupRoute popUpRoute = MockPopupRoute();
  final MockPopupRoute previousPopUpRoute = MockPopupRoute();
  final MockPageRoute pageRoute = MockPageRoute();
  final MockPageRoute previousPageRoute = MockPageRoute();

  group('checking isPopupOnTopStack() when didPush triggered', () {
    setUp(() {
      observer = CommonNavigatorObserver();
    });

    test('topStackIsAPageRoute() return FALSE when push Popup Route', () {
      observer.didPush(popUpRoute, previousPageRoute);
      expect(observer.topStackIsAPageRoute(), false);
    });

    test('topStackIsAPageRoute() return FALSE when push open multi Popup Route', () {
      observer.didPush(popUpRoute, previousPageRoute);
      observer.didPush(MockPopupRoute(), popUpRoute);
      expect(observer.topStackIsAPageRoute(), false);
    });

    test('topStackIsAPageRoute() return TRUE when push PageRoute ', () {
      observer.didPush(pageRoute, previousPageRoute);
      expect(observer.topStackIsAPageRoute(), true);
    });

    test('topStackIsAPageRoute() return TRUE when PopUp is on TopStack and push another PageRoute',
        () {
      observer.didPush(popUpRoute, previousPageRoute);
      expect(observer.topStackIsAPageRoute(), false);

      observer.didPush(MockPageRoute(), popUpRoute);
      expect(observer.topStackIsAPageRoute(), true);
    });
  });

  group('checking isPopupOnTopStack() when didPop triggered', () {
    setUp(() {
      observer = CommonNavigatorObserver();
    });

    test('topStackIsAPageRoute() return FALSE when previousRoute is Popup', () {
      /// setup top of stack is Popup
      observer.topStackRoute = popUpRoute;

      observer.didPop(popUpRoute, previousPopUpRoute);
      expect(observer.topStackIsAPageRoute(), false);
    });

    test('topStackIsAPageRoute() return FALSE when pop from PageRoute to previousPopupRoute', () {
      /// setup top of stack is Page
      observer.topStackRoute = pageRoute;

      observer.didPop(pageRoute, previousPopUpRoute);
      expect(observer.topStackIsAPageRoute(), false);
    });

    test('topStackIsAPageRoute() return TRUE when pop Popup Route to Page Route', () {
      /// setup top of stack is Popup
      observer.topStackRoute = popUpRoute;

      observer.didPop(popUpRoute, previousPageRoute);

      expect(observer.topStackIsAPageRoute(), true);
    });

    test(
        'topStackIsAPageRoute() return FALSE when previousPopupRoute is null (no route to go back)',
        () {
      /// setup top of stack is Page
      observer.topStackRoute = pageRoute;

      observer.didPop(pageRoute, null);
      expect(observer.topStackIsAPageRoute(), false);
    });
  });
}
