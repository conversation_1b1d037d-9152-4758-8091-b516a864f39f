import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../pre_test_setup.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('FirebaseAnalyticsWrapper', () {
    late FirebaseAnalytics mockAnalytics;
    late FirebaseAnalyticsWrapper analyticsWrapper;

    setUp(() {
      // Initialize mocks
      setupFirebaseForTest();
      analyticsWrapper = getIt.get<FirebaseAnalyticsWrapper>();
      mockAnalytics = analyticsWrapper.analytics;

      getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    });

    tearDown(() {
      getIt.unregister<LoggingRepo>();
    });

    test('logEvent sends correct data to FirebaseAnalytics', () async {
      const String eventName = 'test_event';
      final Map<String, String> eventParameters = <String, String>{'key': 'value'};

      when(() => mockAnalytics.logEvent(
            name: eventName,
            parameters: eventParameters,
          )).thenAnswer((_) async {});

      when(() => getIt.get<LoggingRepo>().logEvent(
            eventType: EventType.firebase,
            data: any(named: 'data'),
          )).thenAnswer((_) async {});

      // Call the logEvent method
      await analyticsWrapper.logEvent(name: eventName, parameters: eventParameters);

      verify(() =>
          getIt.get<LoggingRepo>().logEvent(eventType: EventType.firebase, data: <String, dynamic>{
            'event_name': eventName,
            'params': eventParameters,
          })).called(1);

      // Verify that FirebaseAnalytics.logEvent was called with the correct arguments
      verify(() => mockAnalytics.logEvent(
            name: eventName,
            parameters: eventParameters,
          )).called(1);
    });

    test('getAppInstanceId returns the app instance ID when successful', () async {
      // Arrange
      const String expectedInstanceId = 'test-app-instance-id';
      when(() => mockAnalytics.appInstanceId).thenAnswer((_) async => expectedInstanceId);

      // Act
      final String? result = await analyticsWrapper.getAppInstanceId();

      // Assert
      expect(result, equals(expectedInstanceId));
      verify(() => mockAnalytics.appInstanceId).called(1);
    });

    test('getAppInstanceId returns null when an exception occurs', () async {
      // Arrange
      when(() => mockAnalytics.appInstanceId).thenThrow(Exception('Test exception'));

      // Act
      final String? result = await analyticsWrapper.getAppInstanceId();

      // Assert
      expect(result, isNull);
      verify(() => mockAnalytics.appInstanceId).called(1);
    });
  });
}
