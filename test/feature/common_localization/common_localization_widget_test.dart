import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('CommonLocalization', () {
    final List<Locale> supportedLocales = <Locale>[
      const Locale('en', 'US'),
      const Locale('vi', 'VN'),
    ];
    const String path = 'fake_path';

    final Container container = Container();
    final CommonLocalization commonLocalization = CommonLocalization(
      configs: CommonLocalizationConfigs(
        supportedLocales: supportedLocales,
        path: path,
      ),
      child: container,
    );

    testWidgets('should build EasyLocalization with provided parameters',
        (WidgetTester tester) async {
      SharedPreferences.setMockInitialValues(<String, Object>{});
      await CommonLocalization.ensureInitialized();
      await tester.pumpWidget(
        MaterialApp(
          home: commonLocalization,
        ),
      );

      final Finder easyLocalizationFinder = find.byType(EasyLocalization);
      expect(easyLocalizationFinder, findsOneWidget);
      final EasyLocalization easyLocalization =
          tester.widget<EasyLocalization>(easyLocalizationFinder);

      expect(easyLocalization.child, container);
      expect(easyLocalization.supportedLocales, supportedLocales);
      expect(easyLocalization.path, path);
      expect(easyLocalization.fallbackLocale, const Locale('vi', 'VN'));
    });
  });
}
