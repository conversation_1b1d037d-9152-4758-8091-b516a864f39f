import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_frame.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_frame_batch.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EkycBridgeFrameBatch', () {
    test('constructor should initialize all properties', () {
      final List<EkycBridgeFrame> frames = <EkycBridgeFrame>[
        EkycBridgeFrame(
          base64: 'base64',
          index: 0,
          label: 'label',
          metadata: 'metadata',
        ),
        EkycBridgeFrame(
          base64: 'base64',
          index: 1,
          label: 'label',
          metadata: 'metadata',
        ),
      ];
      final Map<String, String> metadata = <String, String>{'key': 'value'};
      final String id = 'batchId';
      final String label = 'batchLabel';

      final EkycBridgeFrameBatch batch = EkycBridgeFrameBatch(
        id: id,
        frames: frames,
        metadata: metadata,
        label: label,
      );

      expect(batch.id, id);
      expect(batch.frames, frames);
      expect(batch.metadata, metadata);
      expect(batch.label, label);
    });

    test('to<PERSON>son should return a map with frames serialized to JSON', () {
      final List<EkycBridgeFrame> frames = <EkycBridgeFrame>[
        EkycBridgeFrame(
          base64: 'base64',
          index: 0,
          label: 'label',
          metadata: 'metadata',
        ),
        EkycBridgeFrame(
          base64: 'base64',
          index: 1,
          label: 'label',
          metadata: 'metadata',
        ),
      ];
      final EkycBridgeFrameBatch batch = EkycBridgeFrameBatch(
        id: 'batchId',
        frames: frames,
        metadata: <String, dynamic>{'key': 'value'},
        label: 'batchLabel',
      );

      final Map<String, dynamic> json = batch.toJson();

      expect(json['frames'], isA<List<Map<String, dynamic>>>());
      expect(json['frames'], hasLength(frames.length));
      for (int i = 0; i < frames.length; i++) {
        expect(json['frames'][i], frames[i].toJson());
      }
    });

    test('toJson should handle null frames gracefully', () {
      final EkycBridgeFrameBatch batch = EkycBridgeFrameBatch(
        id: 'batchId',
        frames: null,
        metadata: <String, dynamic>{'key': 'value'},
        label: 'batchLabel',
      );

      final Map<String, dynamic> json = batch.toJson();

      expect(json['frames'], isNull);
    });
  });
}
