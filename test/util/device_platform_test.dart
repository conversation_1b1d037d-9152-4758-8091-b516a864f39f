import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DevicePlatformImp', () {
    late DevicePlatform devicePlatform;

    setUp(() {
      devicePlatform = DevicePlatformImp();
    });

    tearDown(() {
      debugDefaultTargetPlatformOverride = null;
    });

    test('returns true for Android platform', () {
      debugDefaultTargetPlatformOverride = TargetPlatform.android;
      expect(devicePlatform.isAndroid(), isTrue);
      expect(devicePlatform.isIOS(), isFalse);
    });

    test('returns true for iOS platform', () {
      debugDefaultTargetPlatformOverride = TargetPlatform.iOS;
      expect(devicePlatform.isIOS(), isTrue);
      expect(devicePlatform.isAndroid(), isFalse);
    });

    test('returns false for both on other platforms', () {
      debugDefaultTargetPlatformOverride = TargetPlatform.windows;
      expect(devicePlatform.isAndroid(), isFalse);
      expect(devicePlatform.isIOS(), isFalse);
    });
  });
}