import 'package:flutter_common_package/ui_model/size_image_model.dart';
import 'package:flutter_common_package/util/resize_image_helper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';

class MockResizeImageHelper extends Mock implements ResizeImageHelper {}

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  late ResizeImageHelper resizeImageHelper;
  late double pixelRatio;
  late BuildContext mockContext;

  setUpAll(() {
    resizeImageHelper = ResizeImageHelper();
    pixelRatio = 2.75;
    mockContext = MockBuildContext();
  });

  group('check_convert_value_physical_pixel_of_image_correctly', () {
    test('convert_value_physical_pixel_of_image_correctly', () {
      const double valueConvert = 670;
      final int expectedValueConvert = (valueConvert * pixelRatio).ceil();
      expect(
          expectedValueConvert,
          resizeImageHelper.convertValuePhysicalPixelsImage(mockContext, valueConvert,
              pixelRatio: pixelRatio));
    });

    test('convert_value_physical_pixel_of_image_with_value_is_null', () {
      const double? valueConvert = null;
      const int? expectedValueConvert = null;

      expect(
          expectedValueConvert,
          resizeImageHelper.convertValuePhysicalPixelsImage(mockContext, valueConvert,
              pixelRatio: pixelRatio));
    });

    test('convert_value_physical_pixel_of_image_with_value_is_double.infinity', () {
      const double valueConvert = double.infinity;
      const int? expectedValueConvert = null;

      expect(
          expectedValueConvert,
          resizeImageHelper.convertValuePhysicalPixelsImage(mockContext, valueConvert,
              pixelRatio: pixelRatio));
    });

    test('convert_value_physical_pixel_of_image_with_value_is_negative_value', () {
      const double valueConvert = -1080;
      const int? expectedPhysicalPixelsImage = null;

      expect(
          expectedPhysicalPixelsImage,
          resizeImageHelper.convertValuePhysicalPixelsImage(mockContext, valueConvert,
              pixelRatio: pixelRatio));
    });
  });

  group('check_Resize_Image_correctly', () {
    test('check_Resize_Image_with_memCacheWidth_and_memCacheHeight_not_null', () {
      const int memCacheWidth = 500;
      const int memCacheHeight = 500;
      const int expectedCacheWidth = 500;

      expect(
          const SizeImage(expectedCacheWidth, null),
          resizeImageHelper.cacheImageSize(mockContext,
              memCacheWidth: memCacheWidth,
              memCacheHeight: memCacheHeight,
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600)));
    });
    test('check_Resize_Image_with_memCacheWidth_not_null_and_memCacheHeight_is_null', () {
      const int memCacheWidth = 500;
      const int expectedCacheWidth = 500;

      expect(
          const SizeImage(expectedCacheWidth, null),
          resizeImageHelper.cacheImageSize(mockContext,
              memCacheWidth: memCacheWidth,
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600)));
    });
    test('check_Resize_Image_with_memCacheWidth_is_null_and_memCacheHeight_not_null', () {
      const int memCacheHeight = 500;
      const int expectedCacheHeight = 500;

      expect(
          const SizeImage(null, expectedCacheHeight),
          resizeImageHelper.cacheImageSize(mockContext,
              memCacheHeight: memCacheHeight,
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600)));
    });
    test('check_Resize_Image_with_width_and_height_not_null', () {
      const double width = 600;
      final int expectedCacheWidth = (width * pixelRatio).ceil();

      expect(
          SizeImage(expectedCacheWidth, null),
          resizeImageHelper.cacheImageSize(mockContext,
              width: width,
              height: 600,
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
              pixelRatio: pixelRatio));
    });
    test('check_Resize_Image_with_width_not_null_and_height_is_null', () {
      const double width = 600;
      final int expectedCacheWidth = (width * pixelRatio).ceil();

      expect(
          SizeImage(expectedCacheWidth, null),
          resizeImageHelper.cacheImageSize(mockContext,
              width: width,
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
              pixelRatio: pixelRatio));
    });
    test('check_Resize_Image_with_width_is_null_and_height_not_null', () {
      const double height = 600;
      final int expectedCacheHeight = (height * pixelRatio).ceil();

      expect(
          SizeImage(null, expectedCacheHeight),
          resizeImageHelper.cacheImageSize(mockContext,
              height: height,
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
              pixelRatio: pixelRatio));
    });
    test('check_Resize_Image_with_box_constraint_is_not_double_infinity', () {
      const double maxWidthConstraint = 400;
      final int expectedCacheWidth = (maxWidthConstraint * pixelRatio).ceil();

      expect(
          SizeImage(expectedCacheWidth, null),
          resizeImageHelper.cacheImageSize(mockContext,
              constraints: const BoxConstraints(maxWidth: maxWidthConstraint, maxHeight: 600),
              pixelRatio: pixelRatio));
    });

    test('check_Resize_Image_with_box_constraint_maxWidth_is_not_double_infinity', () {
      const double maxWidthConstraint = 400;
      final int expectedCacheWidth = (maxWidthConstraint * pixelRatio).ceil();

      expect(
          SizeImage(expectedCacheWidth, null),
          resizeImageHelper.cacheImageSize(mockContext,
              constraints: const BoxConstraints(maxWidth: maxWidthConstraint),
              pixelRatio: pixelRatio));
    });

    test('check_Resize_Image_with_box_constraint_maxHeight_is_not_double_infinity', () {
      const double maxHeightConstraint = 600;
      final int expectedCacheHeight = (maxHeightConstraint * pixelRatio).ceil();

      expect(
          SizeImage(null, expectedCacheHeight),
          resizeImageHelper.cacheImageSize(mockContext,
              constraints: const BoxConstraints(maxHeight: maxHeightConstraint),
              pixelRatio: pixelRatio));
    });
  });

  group('cache_size_Image_constraint_correctly', () {
    test('cache_size_Image_constraint_with_maxWidth_bigger_maxHeight_is_not_double_infinity', () {
      const double maxWidthConstraint = 600;
      const double maxHeightConstraint = 400;
      // maxWidthConstraint > maxHeightConstraint => calculator maxHeightConstraint
      final int expectedCacheHeight = (maxHeightConstraint * pixelRatio).ceil();

      expect(
          SizeImage(null, expectedCacheHeight),
          resizeImageHelper.cacheImageSizeConstraint(mockContext,
              constraints: const BoxConstraints(
                  maxWidth: maxWidthConstraint, maxHeight: maxHeightConstraint),
              pixelRatio: pixelRatio));
    });
    test('cache_size_Image_constraint_with_maxWidth_smaller_maxHeight_is_not_double_infinity', () {
      const double maxWidthConstraint = 400;
      const double maxHeightConstraint = 600;
      // maxWidthConstraint < maxHeightConstraint => calculator maxWidthConstraint
      final int expectedCacheWidth = (maxWidthConstraint * pixelRatio).ceil();

      expect(
          SizeImage(expectedCacheWidth, null),
          resizeImageHelper.cacheImageSizeConstraint(mockContext,
              constraints: const BoxConstraints(
                  maxWidth: maxWidthConstraint, maxHeight: maxHeightConstraint),
              pixelRatio: pixelRatio));
    });
    test('cache_size_Image_constraint_with_maxWidth_is_not_double_infinity', () {
      const double maxWidthConstraint = 400;
      final int expectedCacheWidth = (maxWidthConstraint * pixelRatio).ceil();

      expect(
          SizeImage(expectedCacheWidth, null),
          resizeImageHelper.cacheImageSizeConstraint(mockContext,
              constraints: const BoxConstraints(maxWidth: maxWidthConstraint),
              pixelRatio: pixelRatio));
    });
    test('cache_size_Image_constraint_with_maxHeight_is_not_double_infinity', () {
      const double maxHeightConstraint = 600;
      final int expectedCacheHeight = (maxHeightConstraint * pixelRatio).ceil();

      expect(
          SizeImage(null, expectedCacheHeight),
          resizeImageHelper.cacheImageSizeConstraint(mockContext,
              constraints: const BoxConstraints(maxHeight: maxHeightConstraint),
              pixelRatio: pixelRatio));
    });
    test('cache_size_Image_constraint_with_maxHeight_maxWidth_is_double_infinity', () {
      const SizeImage expectedCacheSize = SizeImage(null, null);

      expect(
          expectedCacheSize,
          resizeImageHelper.cacheImageSizeConstraint(mockContext,
              constraints: const BoxConstraints(), pixelRatio: pixelRatio));
    });
  });
}
