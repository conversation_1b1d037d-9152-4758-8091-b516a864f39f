import 'package:flutter_common_package/util/text_input_formatter/numeric_formatter.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';

void main() {
  group('numeric formatter smoke test', _numericFormatterSmokeTest);
}

void _numericFormatterSmokeTest() {
  final ThousandsFormatter thousandsFormatter =
      ThousandsFormatter(CommonValidator(), formatter: NumberFormat.decimalPattern('vi_VN'));

  test('numeric filter smoke test', () {
    final TextEditingValue newValue1 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '500'), const TextEditingValue(text: '500'));

    final TextEditingValue newValue2 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '5000'), const TextEditingValue(text: '5000'));

    final TextEditingValue newValue3 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '50000'), const TextEditingValue(text: '50000'));

    final TextEditingValue newValue4 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '500000'), const TextEditingValue(text: '500000'));

    final TextEditingValue newValue5 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '5000000'), const TextEditingValue(text: '5000000'));

    final TextEditingValue newValue6 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '50000000'), const TextEditingValue(text: '50000000'));

    final TextEditingValue newValue7 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '50000000'), const TextEditingValue(text: '-50000000'));

    final TextEditingValue newValue8 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '50000000'), const TextEditingValue(text: '5000a0000'));

    /// All special character
    final TextEditingValue newValue9 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '50000000'),
        const TextEditingValue(text: '!"#\$%&\'()*+,-./:;<=>?@[]^_`{|}~\\50000000'));

    /// All  character
    final TextEditingValue newValue10 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '50000000'),
        const TextEditingValue(text: 'qwertyuiopasdfghjklzxcvbnm50000000'));

    /// All  character and special character
    final TextEditingValue newValue11 = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '50000000'),
        const TextEditingValue(text: 'qwertyuiopasdfghjklzxcvbnm!"#\$%&\'()*+,-./:;<=>?@[]^_`{|}~\\50000000'));

    expect(newValue1.text, equals('500'));
    expect(newValue2.text, equals('5.000'));
    expect(newValue3.text, equals('50.000'));
    expect(newValue4.text, equals('500.000'));
    expect(newValue5.text, equals('5.000.000'));
    expect(newValue6.text, equals('50.000.000'));
    expect(newValue7.text, equals('50.000.000'));
    expect(newValue8.text, equals('50.000.000'));
    expect(newValue9.text, equals('50.000.000'));
    expect(newValue10.text, equals('50.000.000'));
    expect(newValue11.text, equals('50.000.000'));
  });

  test('thousands formatter with suffix', () {
    final ThousandsFormatter thousandsFormatter = ThousandsFormatter(
      CommonValidator(),
      formatter: NumberFormat.decimalPattern('vi_VN'),
      suffix: 'đ',
    );
    final TextEditingValue result = thousandsFormatter.formatEditUpdate(
        const TextEditingValue(text: '50000000'), const TextEditingValue(text: '50000000'));
    expect(result.text, '50.000.000đ');
  });
}
