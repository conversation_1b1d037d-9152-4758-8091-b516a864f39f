// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/util/permission/device_permission.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:permission_handler/permission_handler.dart';

void main() {
  group('verify TsDevicePermission', () {
    test('has a corresponding Permission for each value', () {
      for (final TsDevicePermission permission in TsDevicePermission.values) {
        expect(TsDevicePermission.listOfPermissions[permission], isA<Permission>());
      }
    });

    test('has unique Permission objects for each value', () {
      final Set<Permission> permissionSet = <Permission>{};

      for (final TsDevicePermission permission in TsDevicePermission.values) {
        final Permission? mappedPermission = TsDevicePermission.listOfPermissions[permission];
        // verify duplicate permissions
        expect(permissionSet.contains(mappedPermission), false);

        if (mappedPermission != null) {
          permissionSet.add(mappedPermission);
        }
      }

      // verify all permissions are mapped
      expect(permissionSet.length, TsDevicePermission.values.length);
    });
  });
}
