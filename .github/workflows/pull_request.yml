# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.
name: Run Code analyze and UnitTest
on:
  pull_request:
    branches: [ "master", "release/*", "hotfix/*" ]

  # entry point trigger this workflow from another workflow
  workflow_call:

jobs:
  build:
    name: Run Code analyze and UnitTest
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          # TS runners:
          # - self-hosted-cpu: cheaper, run 24/7, slower
          # - dedicated-runner: more expensive, only run from 7 AM to 8 PM everyday
          - os: self-hosted-cpu

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0


      - uses: subosito/flutter-action@v2
        with:
          cache: true
          flutter-version: '3.24.5'
          channel: 'stable'

      - name: Clean Cache
        run: flutter clean

      - name: Install dependencies
        run: flutter pub get

      - name: Run analyze
        run: flutter analyze --no-fatal-infos --no-fatal-warnings #Don't throw error for info ans warning issues,

      - name: Test setup
        id: test_setup
        run: |
          FILE_PATH="./.github/unit_test.sh"
          if [ -e "$FILE_PATH" ]; then
            sh $FILE_PATH
          fi
          sudo apt-get update
          sudo apt-get install lcov bc sed gawk -y

      - name: Run UT
        run: flutter test --coverage

      - name: Verify coverage on modified file
        id: check_coverage_modified_file
        run: |

          echo "🔍 Verify coverage on modified lib/* folder"
          echo "==================================================="

          # git diff on lib/* folder and exclude screen
          echo "Base branch: origin/${{ github.event.pull_request.base.ref }}"
          files_string=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}... -- lib/)

          if [ -z "$files_string" ]; then
            echo "✅ No file changed in lib/* folder"
            exit 0
          fi

          while IFS= read -r line; do
            files+=("$line")
          done <<< "$files_string"

          passed=true
          coverage_threshold=80
          temp_lcov="./coverage/filtered_lcov.info"
          lcov_path="./coverage/lcov.info"

          for file in "${files[@]}"; do
            # remove previous temp_lcov
            if [ -e "$temp_lcov" ]; then
              rm $temp_lcov
            fi


            # extract specific file's coverage
            output=$(lcov --extract  "$lcov_path" "$file" --output-file "$temp_lcov" ) || echo "lcov --extract failed"
            output=$(lcov --summary "$temp_lcov" 2>&1) || echo "lcov --summary failed"

            # skip if files\'s coverage unavailable
            if echo "$output" | grep -q "lcov: ERROR: no valid records found in tracefile"; then
              echo "⚠️ $file: file's coverage unavailable"
              continue
            fi
            # exit job if occur unknown lcov error
            if echo "$output" | grep -q "lcov: ERROR:"; then
              echo "==================================================="
              echo "❌ $file: $output"
              exit 2
            fi

            coverage_str=$(echo "$output" | grep "lines......" | cut -d ' ' -f 4 | cut -d '%' -f 1)
            coverage=$(echo "$coverage_str" | bc)

            if echo "$coverage < $coverage_threshold" | bc -l | grep -q 1; then
              file_coverage_msg="❌ $file: $coverage%"
              passed=false
            else
              file_coverage_msg="✅ $file: $coverage%"
            fi

            echo -e "$file_coverage_msg"
          done

          echo "==================================================="
          if [ "$passed" = false ]; then
            echo "❌ Failed to meet file's coverage threshold"
            exit 1
          fi
          echo "✅ Done checking coverage on modified lib/* folder"